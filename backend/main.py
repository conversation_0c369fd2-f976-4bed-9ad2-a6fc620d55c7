#!/usr/bin/env python3
"""
OneClass Platform - Main FastAPI Application
"""
from fastapi import FastAP<PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from datetime import datetime
import logging
import os
from dotenv import load_dotenv

# Import tenant middleware
from shared.middleware.tenant_middleware import TenantMiddleware

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="OneClass Platform API",
    description="Educational Management System for Zimbabwe Schools",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware (before tenant middleware)
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://localhost:3001", 
        "http://frontend:3000",
        "*.oneclass.ac.zw",  # Allow all subdomains
        "https://*.oneclass.ac.zw"
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add tenant isolation middleware
app.add_middleware(TenantMiddleware)

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "OneClass Platform API",
        "status": "running",
        "version": "1.0.0",
        "timestamp": datetime.utcnow().isoformat()
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "oneclass-platform",
        "version": "1.0.0",
        "timestamp": datetime.utcnow().isoformat(),
        "database": "connected" if os.getenv("DATABASE_URL") else "not_configured"
    }

@app.get("/api/health")
async def api_health():
    """API health check"""
    return {
        "status": "healthy",
        "api_version": "v1",
        "timestamp": datetime.utcnow().isoformat()
    }

# Include API Gateway routes
try:
    from api.router import api_router
    from api.subdomain import subdomain_router
    
    # Include main API routes
    app.include_router(api_router)
    app.include_router(subdomain_router)
    
    logger.info("API Gateway routes loaded successfully")
    
except ImportError as e:
    logger.error(f"Failed to load API Gateway routes: {e}")

# Include module-specific routes
try:
    from services.sis.main import router as sis_router

    # Include SIS routes
    app.include_router(sis_router)

    logger.info("SIS module routes loaded successfully")
    
except ImportError as e:
    logger.warning(f"SIS module not available: {e}")

    @app.get("/api/v1/sis/health")
    async def sis_health_fallback():
        """SIS module health check fallback"""
        return {
            "status": "unavailable",
            "service": "sis",
            "error": "Module not loaded",
            "timestamp": datetime.utcnow().isoformat()
        }

# Include Analytics & Reporting module
try:
    from services.analytics.main import analytics_main_router, MODULE_INFO as analytics_info
    
    # Include analytics routes
    app.include_router(analytics_main_router)
    
    @app.get("/api/v1/analytics/health")
    async def analytics_health():
        """Analytics module health check"""
        return {
            "status": "healthy",
            "service": "analytics",
            "version": analytics_info["version"],
            "timestamp": datetime.utcnow().isoformat(),
            "module": analytics_info["name"],
            "features": analytics_info["features"]
        }
    
    logger.info("Analytics & Reporting module loaded successfully")
    
except ImportError as e:
    logger.warning(f"Analytics module not available: {e}")
    
    @app.get("/api/v1/analytics/health")
    async def analytics_health_fallback():
        """Analytics module health check fallback"""
        return {
            "status": "unavailable",
            "service": "analytics",
            "error": "Module not loaded",
            "timestamp": datetime.utcnow().isoformat()
        }

# Include User Management module
try:
    from services.user_management.routes import router as user_management_router
    
    # Include user management routes
    app.include_router(user_management_router, prefix="/api/v1")
    
    @app.get("/api/v1/users/health")
    async def user_management_health():
        """User Management module health check"""
        return {
            "status": "healthy",
            "service": "user_management",
            "version": "1.0.0",
            "timestamp": datetime.utcnow().isoformat(),
            "module": "User Management System",
            "features": [
                "role_based_user_creation",
                "user_invitations",
                "bulk_user_import",
                "user_profile_management",
                "custom_roles"
            ]
        }
    
    logger.info("User Management module loaded successfully")
    
except ImportError as e:
    logger.warning(f"User Management module not available: {e}")
    
    @app.get("/api/v1/users/health")
    async def user_management_health_fallback():
        """User Management module health check fallback"""
        return {
            "status": "unavailable",
            "service": "user_management",
            "error": "Module not loaded",
            "timestamp": datetime.utcnow().isoformat()
        }

# Error handlers
@app.exception_handler(404)
async def not_found_handler(request, exc):
    return JSONResponse(
        status_code=404,
        content={
            "error": "Not Found",
            "message": "The requested resource was not found",
            "path": str(request.url.path)
        }
    )

@app.exception_handler(500)
async def internal_error_handler(request, exc):
    logger.error(f"Internal server error: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal Server Error",
            "message": "An unexpected error occurred"
        }
    )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)