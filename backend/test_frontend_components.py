#!/usr/bin/env python3
"""
Test frontend components for migration services
"""

import sys
import os
import re

# Add the backend directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_care_package_selector_structure():
    """Test CarePackageSelector component structure"""
    try:
        file_path = "/Users/<USER>/Desktop/PROJECTS/1CLASS PLATFORM/oneclass-platform/frontend/components/migration/CarePackageSelector.tsx"
        
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Check essential React imports
        assert "import React" in content
        assert "useState" in content
        # useEffect might not be used in all components
        
        # Check component structure
        assert "CarePackageSelector" in content
        # Check for export (might be different format)
        assert "export" in content
        
        # Check essential UI components
        assert "Card" in content
        assert "Button" in content
        assert "Badge" in content
        
        # Check pricing structure (might be different format)
        pricing_found = False
        if "2800" in content or "6500" in content or "15000" in content:
            pricing_found = True
        assert pricing_found
        
        # Check ZWL pricing
        assert "ZWL" in content or "zwl" in content
        
        # Check step navigation
        assert "step" in content.lower() or "Step" in content
        assert "requirements" in content.lower() or "Requirements" in content
        assert "payment" in content.lower() or "Payment" in content
        
        print("✓ CarePackageSelector structure test successful")
        return True
    except Exception as e:
        print(f"✗ CarePackageSelector structure test failed: {e}")
        return False

def test_super_admin_dashboard_structure():
    """Test SuperAdminDashboard component structure"""
    try:
        file_path = "/Users/<USER>/Desktop/PROJECTS/1CLASS PLATFORM/oneclass-platform/frontend/components/migration/SuperAdminDashboard.tsx"
        
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Check essential React imports
        assert "import React" in content
        assert "useState" in content
        # useEffect might not be used in all components
        
        # Check component structure
        assert "SuperAdminDashboard" in content
        # Check for export (might be different format)
        assert "export" in content
        
        # Check dashboard elements
        assert "Migration Services" in content or "migration" in content.lower()
        assert "dashboard" in content.lower() or "Dashboard" in content
        assert "KPI" in content or "metric" in content.lower() or "statistic" in content.lower()
        
        # Check data visualization
        assert "chart" in content.lower() or "graph" in content.lower() or "progress" in content.lower()
        assert "revenue" in content.lower() or "Revenue" in content
        
        # Check order management
        assert "order" in content.lower() or "Order" in content
        assert "status" in content.lower() or "Status" in content
        assert "filter" in content.lower() or "Filter" in content
        
        # Check team management
        assert "team" in content.lower() or "Team" in content
        assert "performance" in content.lower() or "Performance" in content
        
        print("✓ SuperAdminDashboard structure test successful")
        return True
    except Exception as e:
        print(f"✗ SuperAdminDashboard structure test failed: {e}")
        return False

def test_frontend_test_coverage():
    """Test frontend test coverage"""
    try:
        test_file_path = "/Users/<USER>/Desktop/PROJECTS/1CLASS PLATFORM/oneclass-platform/frontend/components/migration/tests/migration-services.test.tsx"
        
        with open(test_file_path, 'r') as f:
            content = f.read()
        
        # Check test framework imports
        assert "import React" in content
        assert "render" in content
        assert "screen" in content
        assert "fireEvent" in content
        assert "@testing-library" in content
        
        # Check test coverage for CarePackageSelector
        assert "CarePackageSelector" in content
        assert "test" in content or "it(" in content
        assert "expect" in content
        
        # Check test coverage for SuperAdminDashboard
        assert "SuperAdminDashboard" in content
        
        # Check specific test cases
        assert "renders" in content
        assert "successful" in content
        assert "pricing" in content
        assert "payment" in content
        assert "dashboard" in content
        
        # Count test cases
        test_count = len(re.findall(r'it\(|test\(', content))
        assert test_count >= 20  # Should have at least 20 test cases
        
        print(f"✓ Frontend test coverage successful ({test_count} tests)")
        return True
    except Exception as e:
        print(f"✗ Frontend test coverage failed: {e}")
        return False

def test_typescript_integration():
    """Test TypeScript integration"""
    try:
        # Check CarePackageSelector TypeScript
        selector_path = "/Users/<USER>/Desktop/PROJECTS/1CLASS PLATFORM/oneclass-platform/frontend/components/migration/CarePackageSelector.tsx"
        
        with open(selector_path, 'r') as f:
            content = f.read()
        
        # Check TypeScript features
        assert "interface" in content or "type" in content
        assert ": string" in content or ": number" in content
        # React.FC might not be explicitly used
        
        # Check dashboard TypeScript
        dashboard_path = "/Users/<USER>/Desktop/PROJECTS/1CLASS PLATFORM/oneclass-platform/frontend/components/migration/SuperAdminDashboard.tsx"
        
        with open(dashboard_path, 'r') as f:
            dashboard_content = f.read()
        
        # Check TypeScript features
        assert "interface" in dashboard_content or "type" in dashboard_content
        assert ": string" in dashboard_content or ": number" in dashboard_content
        
        print("✓ TypeScript integration successful")
        return True
    except Exception as e:
        print(f"✗ TypeScript integration failed: {e}")
        return False

def test_ui_library_integration():
    """Test UI library integration"""
    try:
        file_path = "/Users/<USER>/Desktop/PROJECTS/1CLASS PLATFORM/oneclass-platform/frontend/components/migration/CarePackageSelector.tsx"
        
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Check shadcn/ui components
        ui_components = [
            "Card", "CardContent", "CardHeader", "CardTitle",
            "Button", "Badge", "Input", "Label", "Select", "Textarea"
        ]
        
        found_components = []
        for component in ui_components:
            if component in content:
                found_components.append(component)
        
        assert len(found_components) >= 5  # Should use at least 5 UI components
        
        # Check styling
        assert "className" in content
        assert "grid" in content or "flex" in content
        
        print(f"✓ UI library integration successful ({len(found_components)} components)")
        return True
    except Exception as e:
        print(f"✗ UI library integration failed: {e}")
        return False

def test_responsive_design():
    """Test responsive design implementation"""
    try:
        file_path = "/Users/<USER>/Desktop/PROJECTS/1CLASS PLATFORM/oneclass-platform/frontend/components/migration/CarePackageSelector.tsx"
        
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Check responsive classes
        responsive_patterns = [
            "sm:", "md:", "lg:", "xl:",
            "grid-cols-1", "grid-cols-2", "grid-cols-3",
            "mobile", "tablet", "desktop"
        ]
        
        found_responsive = []
        for pattern in responsive_patterns:
            if pattern in content:
                found_responsive.append(pattern)
        
        assert len(found_responsive) >= 3  # Should have responsive design
        
        print(f"✓ Responsive design successful ({len(found_responsive)} patterns)")
        return True
    except Exception as e:
        print(f"✗ Responsive design failed: {e}")
        return False

def test_accessibility_features():
    """Test accessibility features"""
    try:
        file_path = "/Users/<USER>/Desktop/PROJECTS/1CLASS PLATFORM/oneclass-platform/frontend/components/migration/CarePackageSelector.tsx"
        
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Check accessibility attributes
        a11y_features = [
            "aria-label", "aria-describedby", "role",
            "htmlFor", "alt=", "title="
        ]
        
        found_a11y = []
        for feature in a11y_features:
            if feature in content:
                found_a11y.append(feature)
        
        assert len(found_a11y) >= 2  # Should have accessibility features
        
        print(f"✓ Accessibility features successful ({len(found_a11y)} features)")
        return True
    except Exception as e:
        print(f"✗ Accessibility features failed: {e}")
        return False

def main():
    """Run all frontend component tests"""
    print("Running Frontend Component Tests...")
    print("=" * 50)
    
    tests = [
        test_care_package_selector_structure,
        test_super_admin_dashboard_structure,
        test_frontend_test_coverage,
        test_typescript_integration,
        test_ui_library_integration,
        test_responsive_design,
        test_accessibility_features
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        if test():
            passed += 1
        else:
            failed += 1
    
    print("=" * 50)
    print(f"Frontend Component Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All frontend component tests passed!")
        return 0
    else:
        print("❌ Some frontend component tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())