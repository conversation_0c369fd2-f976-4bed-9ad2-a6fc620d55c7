# =====================================================
# Consolidated Platform User Model
# Single comprehensive user model replacing all legacy user models
# File: backend/shared/models/platform_user.py
# =====================================================

from pydantic import BaseModel, EmailStr, Field, validator
from typing import List, Optional, Dict, Any, Set
from uuid import UUID, uuid4
from datetime import datetime
from enum import Enum

class PlatformRole(str, Enum):
    """Platform-level roles for users"""
    SUPER_ADMIN = "super_admin"  # Platform administrator
    SCHOOL_ADMIN = "school_admin"  # School administrator
    REGISTRAR = "registrar"  # School registrar
    TEACHER = "teacher"  # Teaching staff
    PARENT = "parent"  # Parent/guardian
    STUDENT = "student"  # Student
    STAFF = "staff"  # Non-teaching staff

class SchoolRole(str, Enum):
    """School-specific roles"""
    PRINCIPAL = "principal"
    DEPUTY_PRINCIPAL = "deputy_principal"
    ACADEMIC_HEAD = "academic_head"
    DEPARTMENT_HEAD = "department_head"
    TEACHER = "teacher"
    FORM_TEACHER = "form_teacher"
    REGISTRAR = "registrar"
    BURSAR = "bursar"
    LIBRARIAN = "librarian"
    IT_SUPPORT = "it_support"
    SECURITY = "security"
    PARENT = "parent"
    STUDENT = "student"

class UserStatus(str, Enum):
    """User account status"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    PENDING_VERIFICATION = "pending_verification"
    ARCHIVED = "archived"

class ClerkIntegration(BaseModel):
    """Clerk authentication integration"""
    clerk_user_id: str
    clerk_session_id: Optional[str] = None
    last_sync: Optional[datetime] = None
    sync_enabled: bool = True
    clerk_metadata: Dict[str, Any] = Field(default_factory=dict)

class UserProfile(BaseModel):
    """Extended user profile information"""
    phone_number: Optional[str] = None
    profile_image_url: Optional[str] = None
    date_of_birth: Optional[datetime] = None
    gender: Optional[str] = None
    address: Optional[str] = None
    emergency_contact_name: Optional[str] = None
    emergency_contact_phone: Optional[str] = None
    bio: Optional[str] = None
    preferred_language: str = "en"
    timezone: str = "Africa/Harare"
    notification_preferences: Dict[str, bool] = Field(default_factory=lambda: {
        "email_notifications": True,
        "sms_notifications": True,
        "push_notifications": True,
        "marketing_emails": False
    })

class SchoolMembership(BaseModel):
    """User's membership in a specific school"""
    school_id: UUID
    school_name: str
    school_subdomain: str
    role: SchoolRole
    permissions: List[str] = Field(default_factory=list)
    joined_date: datetime
    status: UserStatus = UserStatus.ACTIVE
    
    # Academic information for students
    student_id: Optional[str] = None
    current_grade: Optional[str] = None
    admission_date: Optional[datetime] = None
    graduation_date: Optional[datetime] = None
    
    # Employment information for staff
    employee_id: Optional[str] = None
    department: Optional[str] = None
    hire_date: Optional[datetime] = None
    contract_type: Optional[str] = None  # permanent, temporary, contract
    
    # Parent-specific information
    children_ids: List[UUID] = Field(default_factory=list)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v)
        }

class PlatformUser(BaseModel):
    """
    SINGLE, COMPREHENSIVE USER MODEL
    Consolidates all previous user models into one unified structure
    Supports multi-school membership and full feature set
    """
    
    # Core Identity
    id: UUID = Field(default_factory=uuid4)
    email: EmailStr
    first_name: str
    last_name: str
    
    # Platform-level information
    platform_role: PlatformRole = PlatformRole.STUDENT
    status: UserStatus = UserStatus.ACTIVE
    
    # Multi-school support
    school_memberships: List[SchoolMembership] = Field(default_factory=list)
    primary_school_id: Optional[UUID] = None
    
    # Extended profile
    profile: Optional[UserProfile] = None
    
    # Authentication integration
    clerk_integration: Optional[ClerkIntegration] = None
    
    # System metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    last_login: Optional[datetime] = None
    
    # Feature flags and preferences
    feature_flags: Dict[str, bool] = Field(default_factory=dict)
    user_preferences: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v)
        }
    
    @validator('email')
    def validate_email(cls, v):
        """Ensure email is lowercase and valid"""
        return v.lower().strip()
    
    @validator('school_memberships')
    def validate_primary_school(cls, v, values):
        """Ensure primary_school_id exists in memberships"""
        if 'primary_school_id' in values and values['primary_school_id']:
            school_ids = [membership.school_id for membership in v]
            if values['primary_school_id'] not in school_ids:
                raise ValueError("primary_school_id must exist in school_memberships")
        return v
    
    @property
    def full_name(self) -> str:
        """User's full name"""
        return f"{self.first_name} {self.last_name}"
    
    @property
    def display_name(self) -> str:
        """Display name with fallback"""
        if self.profile and hasattr(self.profile, 'display_name') and self.profile.display_name:
            return self.profile.display_name
        return self.full_name
    
    @property
    def is_super_admin(self) -> bool:
        """Check if user is platform super admin"""
        return self.platform_role == PlatformRole.SUPER_ADMIN
    
    @property
    def is_active(self) -> bool:
        """Check if user account is active"""
        return self.status == UserStatus.ACTIVE
    
    @property
    def has_multiple_schools(self) -> bool:
        """Check if user belongs to multiple schools"""
        return len(self.school_memberships) > 1
    
    def get_primary_school_membership(self) -> Optional[SchoolMembership]:
        """Get user's primary school membership"""
        if self.primary_school_id:
            for membership in self.school_memberships:
                if membership.school_id == self.primary_school_id:
                    return membership
        return self.school_memberships[0] if self.school_memberships else None
    
    def get_school_membership(self, school_id: UUID) -> Optional[SchoolMembership]:
        """Get membership for specific school"""
        for membership in self.school_memberships:
            if membership.school_id == school_id:
                return membership
        return None
    
    def has_permission_in_school(self, permission: str, school_id: UUID) -> bool:
        """Check if user has specific permission in school"""
        if self.is_super_admin:
            return True
        
        membership = self.get_school_membership(school_id)
        if not membership:
            return False
        
        return permission in membership.permissions or "*" in membership.permissions
    
    def has_role_in_school(self, role: SchoolRole, school_id: UUID) -> bool:
        """Check if user has specific role in school"""
        membership = self.get_school_membership(school_id)
        return membership.role == role if membership else False
    
    def can_access_school(self, school_id: UUID) -> bool:
        """Check if user can access specific school"""
        if self.is_super_admin:
            return True
        
        membership = self.get_school_membership(school_id)
        return membership is not None and membership.status == UserStatus.ACTIVE
    
    def add_school_membership(self, membership: SchoolMembership) -> None:
        """Add new school membership"""
        # Remove existing membership for same school if exists
        self.school_memberships = [
            m for m in self.school_memberships 
            if m.school_id != membership.school_id
        ]
        self.school_memberships.append(membership)
        
        # Set as primary if it's the first school
        if not self.primary_school_id:
            self.primary_school_id = membership.school_id
        
        self.updated_at = datetime.utcnow()
    
    def remove_school_membership(self, school_id: UUID) -> bool:
        """Remove school membership"""
        initial_count = len(self.school_memberships)
        self.school_memberships = [
            m for m in self.school_memberships 
            if m.school_id != school_id
        ]
        
        # Update primary school if removed
        if self.primary_school_id == school_id:
            self.primary_school_id = (
                self.school_memberships[0].school_id 
                if self.school_memberships else None
            )
        
        self.updated_at = datetime.utcnow()
        return len(self.school_memberships) < initial_count
    
    def update_profile(self, profile_updates: Dict[str, Any]) -> None:
        """Update user profile"""
        if not self.profile:
            self.profile = UserProfile()
        
        for key, value in profile_updates.items():
            if hasattr(self.profile, key):
                setattr(self.profile, key, value)
        
        self.updated_at = datetime.utcnow()
    
    def set_clerk_integration(self, clerk_user_id: str, metadata: Dict[str, Any] = None) -> None:
        """Set up Clerk integration"""
        self.clerk_integration = ClerkIntegration(
            clerk_user_id=clerk_user_id,
            last_sync=datetime.utcnow(),
            clerk_metadata=metadata or {}
        )
        self.updated_at = datetime.utcnow()
    
    def update_last_login(self) -> None:
        """Update last login timestamp"""
        self.last_login = datetime.utcnow()
        self.updated_at = datetime.utcnow()
    
    def to_minimal_context(self, school_id: Optional[UUID] = None) -> Dict[str, Any]:
        """Convert to minimal context for performance-critical operations"""
        membership = (
            self.get_school_membership(school_id) 
            if school_id else self.get_primary_school_membership()
        )
        
        return {
            "user_id": self.id,
            "email": self.email,
            "full_name": self.full_name,
            "platform_role": self.platform_role,
            "school_role": membership.role if membership else None,
            "school_id": school_id or self.primary_school_id,
            "permissions": membership.permissions if membership else [],
            "is_active": self.is_active
        }

class UserInvitation(BaseModel):
    """User invitation model for new and existing users"""
    
    id: UUID = Field(default_factory=uuid4)
    email: EmailStr
    school_id: UUID
    
    # Roles
    invited_role: PlatformRole
    school_role: SchoolRole
    
    # Invitation details
    invitation_token: str
    invitation_type: str  # "new_user" or "existing_user"
    status: str = "pending"  # pending, accepted, declined, expired
    
    # User references
    inviter_id: UUID
    existing_user_id: Optional[UUID] = None
    
    # Additional context
    additional_context: Dict[str, Any] = Field(default_factory=dict)
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    expires_at: datetime
    accepted_at: Optional[datetime] = None
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v)
        }
    
    @property
    def is_expired(self) -> bool:
        """Check if invitation has expired"""
        return datetime.utcnow() > self.expires_at
    
    @property
    def is_pending(self) -> bool:
        """Check if invitation is still pending and valid"""
        return self.status == "pending" and not self.is_expired

class UserSession(BaseModel):
    """User session model for authentication tracking"""
    
    id: UUID = Field(default_factory=uuid4)
    user_id: UUID
    session_id: str
    refresh_token: str
    
    # Session metadata
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    device_info: Dict[str, Any] = Field(default_factory=dict)
    
    # School context
    current_school_id: Optional[UUID] = None
    
    # Session status
    is_active: bool = True
    last_activity: datetime = Field(default_factory=datetime.utcnow)
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    expires_at: datetime
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v)
        }
    
    @property
    def is_expired(self) -> bool:
        """Check if session has expired"""
        return datetime.utcnow() > self.expires_at
    
    @property
    def is_valid(self) -> bool:
        """Check if session is valid and active"""
        return self.is_active and not self.is_expired

# Database models for SQLAlchemy
from sqlalchemy import Column, String, Boolean, DateTime, Text, JSON, ForeignKey
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

Base = declarative_base()

class PlatformUserDB(Base):
    """SQLAlchemy model for PlatformUser"""
    __tablename__ = "platform_users"
    __table_args__ = {"schema": "platform"}
    
    id = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email = Column(String(255), unique=True, nullable=False, index=True)
    first_name = Column(String(100), nullable=False)
    last_name = Column(String(100), nullable=False)
    
    platform_role = Column(String(50), nullable=False, default="student")
    status = Column(String(50), nullable=False, default="active")
    
    primary_school_id = Column(PGUUID(as_uuid=True), index=True)
    
    # JSON fields for complex data
    profile = Column(JSON, default={})
    clerk_integration = Column(JSON, default={})
    feature_flags = Column(JSON, default={})
    user_preferences = Column(JSON, default={})
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    last_login = Column(DateTime(timezone=True))
    
    # Relationships
    school_memberships = relationship("SchoolMembershipDB", back_populates="user")
    
    def __repr__(self):
        return f"<PlatformUser(id={self.id}, email='{self.email}', role='{self.platform_role}')>"

class SchoolMembershipDB(Base):
    """SQLAlchemy model for SchoolMembership"""
    __tablename__ = "school_memberships"
    __table_args__ = {"schema": "platform"}
    
    id = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(PGUUID(as_uuid=True), ForeignKey("platform.platform_users.id"), nullable=False)
    school_id = Column(PGUUID(as_uuid=True), nullable=False, index=True)
    
    school_name = Column(String(255), nullable=False)
    school_subdomain = Column(String(50), nullable=False)
    role = Column(String(50), nullable=False)
    permissions = Column(JSON, default=[])
    joined_date = Column(DateTime(timezone=True), nullable=False)
    status = Column(String(50), nullable=False, default="active")
    
    # Role-specific data
    student_id = Column(String(50))
    current_grade = Column(String(20))
    admission_date = Column(DateTime(timezone=True))
    graduation_date = Column(DateTime(timezone=True))
    
    employee_id = Column(String(50))
    department = Column(String(100))
    hire_date = Column(DateTime(timezone=True))
    contract_type = Column(String(50))
    
    children_ids = Column(JSON, default=[])
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    user = relationship("PlatformUserDB", back_populates="school_memberships")
    
    def __repr__(self):
        return f"<SchoolMembership(user_id={self.user_id}, school_id={self.school_id}, role='{self.role}')>"

class UserInvitationDB(Base):
    """SQLAlchemy model for UserInvitation"""
    __tablename__ = "user_invitations"
    __table_args__ = {"schema": "platform"}
    
    id = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email = Column(String(255), nullable=False, index=True)
    school_id = Column(PGUUID(as_uuid=True), nullable=False, index=True)
    
    # Roles
    invited_role = Column(String(50), nullable=False)  # PlatformRole
    school_role = Column(String(50), nullable=False)   # SchoolRole
    
    # Invitation details
    invitation_token = Column(String(255), unique=True, nullable=False, index=True)
    invitation_type = Column(String(50), nullable=False)  # "new_user" or "existing_user"
    status = Column(String(50), nullable=False, default="pending")  # pending, accepted, declined, expired
    
    # User references
    inviter_id = Column(PGUUID(as_uuid=True), nullable=False)
    existing_user_id = Column(PGUUID(as_uuid=True), nullable=True)  # If inviting existing user
    
    # Additional context
    additional_context = Column(JSON, default={})  # Department, employee_id, etc.
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    expires_at = Column(DateTime(timezone=True), nullable=False)
    accepted_at = Column(DateTime(timezone=True), nullable=True)
    
    def __repr__(self):
        return f"<UserInvitation(email='{self.email}', school_id={self.school_id}, status='{self.status}')>"

class UserSessionDB(Base):
    """SQLAlchemy model for UserSession"""
    __tablename__ = "user_sessions"
    __table_args__ = {"schema": "platform"}
    
    id = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(PGUUID(as_uuid=True), ForeignKey("platform.platform_users.id"), nullable=False)
    session_id = Column(String(255), unique=True, nullable=False, index=True)
    refresh_token = Column(String(500), unique=True, nullable=False, index=True)
    
    # Session metadata
    ip_address = Column(String(50))
    user_agent = Column(Text)
    device_info = Column(JSON, default={})
    
    # School context
    current_school_id = Column(PGUUID(as_uuid=True), nullable=True)
    
    # Session status
    is_active = Column(Boolean, default=True)
    last_activity = Column(DateTime(timezone=True), server_default=func.now())
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    expires_at = Column(DateTime(timezone=True), nullable=False)
    
    # Relationships
    user = relationship("PlatformUserDB")
    
    def __repr__(self):
        return f"<UserSession(user_id={self.user_id}, session_id='{self.session_id[:8]}...', active={self.is_active})>"