"""Tenant Isolation Middleware
Middleware for multi-tenant request processing and data isolation"""

from fastapi import Request, Response, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text
from typing import Callable, Optional, Dict, Any
import re
import logging
from datetime import datetime

from shared.models.platform import School
from shared.auth import verify_token, UserSession, db_manager

logger = logging.getLogger(__name__)


class TenantContext:
    """Context object for tenant information"""
    
    def __init__(
        self,
        school_id: str,
        school_name: str,
        subdomain: str,
        subscription_tier: str,
        enabled_modules: list,
        user_session: Optional[UserSession] = None
    ):
        self.school_id = school_id
        self.school_name = school_name
        self.subdomain = subdomain
        self.subscription_tier = subscription_tier
        self.enabled_modules = enabled_modules
        self.user_session = user_session
        self.request_id = None
        self.timestamp = datetime.utcnow()
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "school_id": self.school_id,
            "school_name": self.school_name,
            "subdomain": self.subdomain,
            "subscription_tier": self.subscription_tier,
            "enabled_modules": self.enabled_modules,
            "user_session": self.user_session.dict() if self.user_session else None,
            "request_id": self.request_id,
            "timestamp": self.timestamp.isoformat()
        }


class TenantMiddleware(BaseHTTPMiddleware):
    """Middleware for tenant isolation and context injection"""
    
    def __init__(self, app, db_session_factory=None):
        super().__init__(app)
        self.db_session_factory = db_session_factory or self._get_db_connection
        
        # Routes that don't require tenant context
        self.public_routes = {
            '/',
            '/health',
            '/docs',
            '/redoc',
            '/openapi.json',
            '/api/health',
            '/api/v1/platform/schools',  # School creation
            '/api/v1/platform/schools/by-subdomain',  # Subdomain lookup
            '/api/v1/schools/validate-subdomain',
            '/api/v1/schools/suggest-subdomains',
            '/api/v1/schools/by-subdomain',
            '/api/v1/schools/onboard',
            '/api/v1/auth/login',
            '/api/v1/auth/signup',
            '/api/v1/auth/refresh',
            '/api/v1/sis/health',
            '/api/v1/analytics/health'
        }
        
        # Platform admin routes (no tenant isolation)
        self.platform_routes = {
            '/api/v1/platform',
            '/api/v1/admin'
        }
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request with tenant context"""
        start_time = datetime.utcnow()
        
        # Generate request ID for tracing
        request_id = self._generate_request_id()
        request.state.request_id = request_id
        
        try:
            # Skip tenant processing for public routes
            if self._is_public_route(request.url.path):
                response = await call_next(request)
                self._add_response_headers(response, request_id=request_id)
                return response
            
            # Skip tenant processing for platform admin routes
            if self._is_platform_route(request.url.path):
                # Still validate platform admin authentication
                await self._validate_platform_admin(request)
                response = await call_next(request)
                self._add_response_headers(response, request_id=request_id)
                return response
            
            # Extract tenant context
            tenant_context = await self._extract_tenant_context(request)
            
            if not tenant_context:
                return JSONResponse(
                    status_code=400,
                    content={
                        "error": "missing_tenant_context",
                        "message": "Unable to determine school context for this request",
                        "request_id": request_id
                    }
                )
            
            # Inject tenant context into request state
            request.state.tenant = tenant_context
            request.state.school_id = tenant_context.school_id
            
            # Set up database session with RLS context
            await self._setup_database_context(request, tenant_context)
            
            # Validate module access
            if not self._validate_module_access(request, tenant_context):
                return JSONResponse(
                    status_code=403,
                    content={
                        "error": "module_not_available",
                        "message": "The requested module is not available in your subscription",
                        "subscription_tier": tenant_context.subscription_tier,
                        "request_id": request_id
                    }
                )
            
            # Process request
            response = await call_next(request)
            
            # Add tenant context to response headers
            self._add_response_headers(
                response, 
                tenant_context=tenant_context,
                request_id=request_id
            )
            
            # Log request completion
            duration = (datetime.utcnow() - start_time).total_seconds()
            logger.info(
                f"Request completed: {request.method} {request.url.path} "
                f"[{tenant_context.school_id}] - {duration:.3f}s"
            )
            
            return response
            
        except Exception as e:
            logger.error(
                f"Tenant middleware error: {str(e)} - {request.method} {request.url.path}",
                exc_info=True
            )
            
            return JSONResponse(
                status_code=500,
                content={
                    "error": "tenant_middleware_error",
                    "message": "An error occurred while processing tenant context",
                    "request_id": request_id
                }
            )
    
    async def _extract_tenant_context(self, request: Request) -> Optional[TenantContext]:
        """Extract tenant context from request headers"""
        # Try to get school context from headers (injected by frontend middleware)
        school_id = request.headers.get('X-School-ID')
        school_name = request.headers.get('X-School-Name')
        subdomain = request.headers.get('X-School-Subdomain')
        subscription_tier = request.headers.get('X-School-Tier')
        
        if school_id and school_name and subdomain:
            # Get enabled modules (may need to fetch from database)
            enabled_modules = await self._get_school_modules(school_id)
            
            # Extract user session if available
            user_session = await self._extract_user_session(request)
            
            return TenantContext(
                school_id=school_id,
                school_name=school_name,
                subdomain=subdomain,
                subscription_tier=subscription_tier or 'basic',
                enabled_modules=enabled_modules,
                user_session=user_session
            )
        
        # Fallback: try to extract from subdomain in host header
        host = request.headers.get('host', '')
        subdomain = self._extract_subdomain_from_host(host)
        
        if subdomain:
            return await self._get_tenant_by_subdomain(subdomain, request)
        
        return None
    
    async def _extract_user_session(self, request: Request) -> Optional[UserSession]:
        """Extract user session from request"""
        # Try to get from headers first (injected by frontend middleware)
        user_id = request.headers.get('X-User-ID')
        user_role = request.headers.get('X-User-Role')
        user_permissions = request.headers.get('X-User-Permissions')
        user_features = request.headers.get('X-User-Features')
        
        if user_id and user_role:
            try:
                import json
                permissions = json.loads(user_permissions) if user_permissions else []
                features = json.loads(user_features) if user_features else []
                
                return UserSession(
                    user_id=user_id,
                    role=user_role,
                    permissions=permissions,
                    features=features,
                    school_id=request.headers.get('X-School-ID')
                )
            except (json.JSONDecodeError, TypeError):
                pass
        
        # Fallback: try to extract from Authorization header or cookies
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header[7:]
            return await verify_token(token)
        
        # Try cookies
        token = request.cookies.get('auth-token')
        if token:
            return await verify_token(token)
        
        return None
    
    async def _get_db_connection(self):
        """Get database connection using auth module's db manager"""
        return db_manager.get_connection()
    
    async def _get_tenant_by_subdomain(self, subdomain: str, request: Request) -> Optional[TenantContext]:
        """Get tenant context by subdomain"""
        async with db_manager.get_connection() as db:
            query = """
                SELECT id, name, subdomain, subscription_tier, status, is_active
                FROM platform.schools 
                WHERE subdomain = $1 AND is_active = true
            """
            
            result = await db.fetchrow(query, subdomain.lower())
            
            if not result:
                return None
            
            enabled_modules = await self._get_school_modules(str(result["id"]))
            user_session = await self._extract_user_session(request)
            
            return TenantContext(
                school_id=str(result["id"]),
                school_name=result["name"],
                subdomain=result["subdomain"],
                subscription_tier=result["subscription_tier"] or "basic",
                enabled_modules=enabled_modules,
                user_session=user_session
            )
    
    async def _get_school_modules(self, school_id: str) -> list:
        """Get enabled modules for school"""
        try:
            async with db_manager.get_connection() as db:
                # Fetch enabled modules from school configuration
                query = """
                    SELECT sc.enabled_modules
                    FROM platform.school_configurations sc
                    WHERE sc.school_id = $1
                """
                result = await db.fetchval(query, school_id)
                
                if result:
                    # Parse JSON string if needed
                    if isinstance(result, str):
                        import json
                        try:
                            return json.loads(result)
                        except json.JSONDecodeError:
                            logger.warning(f"Invalid JSON in enabled_modules for school {school_id}")
                    elif isinstance(result, list):
                        return result
                
                # Fallback to default modules if no configuration found
                return [
                    'student_information_system',
                    'finance_management',
                    'academic_management'
                ]
        except Exception as e:
            logger.warning(f"Failed to fetch school modules for {school_id}: {e}")
            # Return default modules on error
            return [
                'student_information_system',
                'finance_management',
                'academic_management'
            ]
    
    async def _setup_database_context(self, request: Request, tenant_context: TenantContext):
        """Set up database context for Row Level Security"""
        # This would set RLS context variables
        # Implementation depends on your database setup
        request.state.db_context = {
            'school_id': tenant_context.school_id,
            'user_id': tenant_context.user_session.user_id if tenant_context.user_session else None
        }
    
    async def _validate_platform_admin(self, request: Request):
        """Validate platform admin access"""
        user_session = await self._extract_user_session(request)
        
        if not user_session or user_session.role != 'platform_admin':
            raise HTTPException(
                status_code=403,
                detail={
                    "error": "platform_admin_required",
                    "message": "Platform administrator access required"
                }
            )
    
    def _validate_module_access(self, request: Request, tenant_context: TenantContext) -> bool:
        """Validate if the requested module is available for the tenant"""
        path = request.url.path
        
        # Extract module from path
        if '/finance/' in path or path.startswith('/api/v1/finance'):
            return 'finance_management' in tenant_context.enabled_modules
        elif '/academic/' in path or path.startswith('/api/v1/academic'):
            return 'academic_management' in tenant_context.enabled_modules
        elif '/sis/' in path or path.startswith('/api/v1/sis'):
            return 'student_information_system' in tenant_context.enabled_modules
        
        # Default: allow access
        return True
    
    def _is_public_route(self, path: str) -> bool:
        """Check if route is public"""
        return any(path.startswith(route) for route in self.public_routes)
    
    def _is_platform_route(self, path: str) -> bool:
        """Check if route is platform admin route"""
        return any(path.startswith(route) for route in self.platform_routes)
    
    def _extract_subdomain_from_host(self, host: str) -> Optional[str]:
        """Extract subdomain from host header"""
        if not host:
            return None
        
        # Remove port if present
        clean_host = host.split(':')[0]
        
        # Skip localhost
        if clean_host in ['localhost', '127.0.0.1']:
            return None
        
        parts = clean_host.split('.')
        if len(parts) >= 3 and parts[0] != 'www':
            return parts[0]
        
        return None
    
    def _generate_request_id(self) -> str:
        """Generate unique request ID"""
        import uuid
        return str(uuid.uuid4())[:8]
    
    def _add_response_headers(
        self, 
        response: Response, 
        tenant_context: Optional[TenantContext] = None,
        request_id: Optional[str] = None
    ):
        """Add tenant context and tracking headers to response"""
        if request_id:
            response.headers['X-Request-ID'] = request_id
        
        if tenant_context:
            response.headers['X-Tenant-ID'] = tenant_context.school_id
            response.headers['X-Tenant-Name'] = tenant_context.school_name
            response.headers['X-Tenant-Tier'] = tenant_context.subscription_tier
        
        response.headers['X-Service'] = 'oneclass-platform'
        response.headers['X-Timestamp'] = datetime.utcnow().isoformat()


# Helper function to get tenant context from request
def get_tenant_context(request: Request) -> TenantContext:
    """Get tenant context from request state"""
    if not hasattr(request.state, 'tenant'):
        raise HTTPException(
            status_code=500,
            detail={
                "error": "missing_tenant_context",
                "message": "Tenant context not found in request"
            }
        )
    
    return request.state.tenant


# Helper function to get school ID from request
def get_school_id(request: Request) -> str:
    """Get school ID from request state"""
    tenant = get_tenant_context(request)
    return tenant.school_id


# Helper function to get user session from request
def get_user_session(request: Request) -> Optional[UserSession]:
    """Get user session from request state"""
    tenant = get_tenant_context(request)
    return tenant.user_session
