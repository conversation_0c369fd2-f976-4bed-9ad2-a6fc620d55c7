# =====================================================
# User Migration Service for Zero-Downtime Migration
# Handles migration from legacy user models to consolidated PlatformUser
# File: backend/shared/services/user_migration_service.py
# =====================================================

import asyncio
import json
import logging
import math
from typing import Optional, List, Dict, Any, Tuple
from uuid import UUID, uuid4
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

# Database imports
import asyncpg
from sqlalchemy import select, and_, or_, func, text, update, delete
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

# Models
from shared.models.platform_user import (
    PlatformUser, SchoolMembership, UserProfile, ClerkIntegration,
    PlatformRole, SchoolRole, UserStatus,
    PlatformUserDB, SchoolMembershipDB
)
from shared.cache.user_context_cache import UserContextCache

logger = logging.getLogger(__name__)


class MigrationType(str, Enum):
    """Types of user migrations"""
    LEGACY_TO_PLATFORM = "legacy_to_platform"
    ENHANCED_TO_PLATFORM = "enhanced_to_platform"
    MERGE_DUPLICATES = "merge_duplicates"
    DATA_CLEANUP = "data_cleanup"


class MigrationStatus(str, Enum):
    """Migration batch status"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    ROLLED_BACK = "rolled_back"


@dataclass
class MigrationBatchResult:
    """Result of a migration batch"""
    batch_number: int
    migration_type: MigrationType
    total_records: int
    migrated_count: int
    error_count: int
    skipped_count: int
    success_rate: float
    errors: List[Dict[str, Any]]
    duration_seconds: float
    started_at: datetime
    completed_at: datetime


@dataclass
class MigrationPlan:
    """Comprehensive migration plan"""
    total_users: int
    batches: int
    estimated_duration_minutes: int
    migration_types: List[MigrationType]
    pre_migration_checks: List[str]
    migration_steps: List[str]
    post_migration_validation: List[str]
    rollback_plan: List[str]
    risk_assessment: Dict[str, str]


@dataclass
class DataQualityIssue:
    """Data quality issue found during analysis"""
    issue_type: str
    table_name: str
    record_count: int
    description: str
    severity: str  # low, medium, high, critical
    auto_fixable: bool
    fix_strategy: Optional[str] = None


class UserMigrationService:
    """
    Comprehensive service for migrating from legacy user models to consolidated PlatformUser
    Implements zero-downtime migration with batch processing, validation, and rollback capabilities
    """
    
    def __init__(self, db_session: AsyncSession, cache: UserContextCache, batch_size: int = 100):
        """
        Initialize migration service
        
        Args:
            db_session: SQLAlchemy async session
            cache: UserContextCache instance
            batch_size: Number of records to process per batch
        """
        self.db = db_session
        self.cache = cache
        self.batch_size = batch_size
        self.logger = logger
        
        # Migration configuration
        self.validation_enabled = True
        self.backup_enabled = True
        self.rollback_enabled = True
        
        # Performance settings
        self.max_concurrent_batches = 3
        self.batch_delay_seconds = 0.1  # Prevent database overload
    
    # Analysis and Planning
    
    async def analyze_existing_data(self) -> Dict[str, Any]:
        """
        Comprehensive analysis of existing user data
        Identifies data quality issues, duplicates, and migration complexity
        """
        self.logger.info("Starting comprehensive data analysis...")
        
        analysis = {
            "timestamp": datetime.utcnow().isoformat(),
            "legacy_users": await self._analyze_legacy_users(),
            "enhanced_users": await self._analyze_enhanced_users(),
            "platform_users": await self._analyze_platform_users(),
            "data_quality_issues": await self._analyze_data_quality(),
            "duplicates": await self._analyze_duplicates(),
            "orphaned_records": await self._analyze_orphaned_records(),
            "foreign_key_integrity": await self._analyze_foreign_keys(),
            "recommendations": []
        }
        
        # Generate recommendations based on analysis
        analysis["recommendations"] = self._generate_recommendations(analysis)
        
        self.logger.info(f"Data analysis completed. Found {len(analysis['data_quality_issues'])} data quality issues")
        return analysis
    
    async def _analyze_legacy_users(self) -> Dict[str, Any]:
        """Analyze legacy users table if it exists"""
        try:
            # Check if legacy users table exists
            query = text("""
                SELECT COUNT(*) as total_count,
                       COUNT(DISTINCT email) as unique_emails,
                       COUNT(CASE WHEN email IS NULL OR email = '' THEN 1 END) as missing_emails,
                       COUNT(CASE WHEN first_name IS NULL OR first_name = '' THEN 1 END) as missing_first_names,
                       COUNT(CASE WHEN last_name IS NULL OR last_name = '' THEN 1 END) as missing_last_names,
                       MIN(created_at) as earliest_user,
                       MAX(created_at) as latest_user
                FROM platform.users
                WHERE EXISTS (SELECT 1 FROM information_schema.tables 
                             WHERE table_schema = 'platform' AND table_name = 'users')
            """)
            
            result = await self.db.execute(query)
            row = result.first()
            
            if row and row.total_count > 0:
                return {
                    "exists": True,
                    "total_count": row.total_count,
                    "unique_emails": row.unique_emails,
                    "missing_emails": row.missing_emails,
                    "missing_first_names": row.missing_first_names,
                    "missing_last_names": row.missing_last_names,
                    "earliest_user": row.earliest_user,
                    "latest_user": row.latest_user,
                    "duplicate_emails": row.total_count - row.unique_emails
                }
            
        except Exception as e:
            self.logger.warning(f"Error analyzing legacy users: {e}")
        
        return {"exists": False, "total_count": 0}
    
    async def _analyze_enhanced_users(self) -> Dict[str, Any]:
        """Analyze enhanced users data if it exists"""
        try:
            # This would analyze the enhanced user model data
            # For now, return placeholder data
            return {"exists": False, "total_count": 0}
        except Exception as e:
            self.logger.warning(f"Error analyzing enhanced users: {e}")
            return {"exists": False, "total_count": 0}
    
    async def _analyze_platform_users(self) -> Dict[str, Any]:
        """Analyze existing platform users"""
        try:
            query = text("""
                SELECT 
                    COUNT(*) as total_count,
                    COUNT(DISTINCT email) as unique_emails,
                    COUNT(CASE WHEN primary_school_id IS NOT NULL THEN 1 END) as with_primary_school,
                    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users,
                    COUNT(CASE WHEN clerk_integration IS NOT NULL AND clerk_integration != '{}' THEN 1 END) as with_clerk_integration
                FROM platform.platform_users
            """)
            
            result = await self.db.execute(query)
            row = result.first()
            
            return {
                "total_count": row.total_count or 0,
                "unique_emails": row.unique_emails or 0,
                "with_primary_school": row.with_primary_school or 0,
                "active_users": row.active_users or 0,
                "with_clerk_integration": row.with_clerk_integration or 0
            }
            
        except Exception as e:
            self.logger.warning(f"Error analyzing platform users: {e}")
            return {"total_count": 0}
    
    async def _analyze_data_quality(self) -> List[DataQualityIssue]:
        """Analyze data quality issues"""
        issues = []
        
        try:
            # Check for missing required fields
            query = text("""
                SELECT 
                    'missing_email' as issue_type,
                    COUNT(*) as count
                FROM platform.users 
                WHERE email IS NULL OR email = ''
                UNION ALL
                SELECT 
                    'missing_names' as issue_type,
                    COUNT(*) as count
                FROM platform.users 
                WHERE first_name IS NULL OR first_name = '' OR last_name IS NULL OR last_name = ''
                UNION ALL
                SELECT 
                    'invalid_email_format' as issue_type,
                    COUNT(*) as count
                FROM platform.users 
                WHERE email IS NOT NULL AND email != '' AND email NOT LIKE '%@%'
            """)
            
            result = await self.db.execute(query)
            for row in result:
                if row.count > 0:
                    issues.append(DataQualityIssue(
                        issue_type=row.issue_type,
                        table_name="platform.users",
                        record_count=row.count,
                        description=f"Found {row.count} records with {row.issue_type}",
                        severity="high" if row.issue_type == "missing_email" else "medium",
                        auto_fixable=row.issue_type == "invalid_email_format"
                    ))
            
        except Exception as e:
            self.logger.warning(f"Error analyzing data quality: {e}")
        
        return issues
    
    async def _analyze_duplicates(self) -> Dict[str, Any]:
        """Analyze duplicate records"""
        try:
            query = text("""
                SELECT email, COUNT(*) as count
                FROM platform.users 
                WHERE email IS NOT NULL AND email != ''
                GROUP BY email 
                HAVING COUNT(*) > 1
                ORDER BY count DESC
                LIMIT 100
            """)
            
            result = await self.db.execute(query)
            duplicates = [{"email": row.email, "count": row.count} for row in result]
            
            return {
                "duplicate_emails": len(duplicates),
                "total_duplicate_records": sum(dup["count"] - 1 for dup in duplicates),
                "top_duplicates": duplicates[:10]
            }
            
        except Exception as e:
            self.logger.warning(f"Error analyzing duplicates: {e}")
            return {"duplicate_emails": 0, "total_duplicate_records": 0}
    
    async def _analyze_orphaned_records(self) -> Dict[str, Any]:
        """Analyze orphaned records"""
        # This would check for records that reference non-existent schools, etc.
        return {"orphaned_school_references": 0}
    
    async def _analyze_foreign_keys(self) -> Dict[str, Any]:
        """Analyze foreign key integrity"""
        # This would check foreign key constraints
        return {"broken_foreign_keys": 0}
    
    def _generate_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on analysis"""
        recommendations = []
        
        # Check for critical issues
        if analysis["data_quality_issues"]:
            critical_issues = [issue for issue in analysis["data_quality_issues"] if issue.severity == "critical"]
            if critical_issues:
                recommendations.append("❌ CRITICAL: Fix data quality issues before migration")
        
        # Check for duplicates
        if analysis["duplicates"]["duplicate_emails"] > 0:
            recommendations.append(f"⚠️  Resolve {analysis['duplicates']['duplicate_emails']} duplicate email addresses")
        
        # Check migration complexity
        total_users = analysis["legacy_users"]["total_count"] + analysis["enhanced_users"]["total_count"]
        if total_users > 10000:
            recommendations.append("📊 Large migration detected - consider running during off-peak hours")
        
        # Performance recommendations
        if total_users > 1000:
            recommendations.append("🚀 Enable Redis caching to improve performance")
        
        if not recommendations:
            recommendations.append("✅ Data looks good - ready for migration")
        
        return recommendations
    
    async def create_migration_plan(self) -> MigrationPlan:
        """Create detailed migration execution plan"""
        self.logger.info("Creating migration plan...")
        
        analysis = await self.analyze_existing_data()
        total_users = analysis["legacy_users"]["total_count"] + analysis["enhanced_users"]["total_count"]
        
        plan = MigrationPlan(
            total_users=total_users,
            batches=math.ceil(total_users / self.batch_size) if total_users > 0 else 0,
            estimated_duration_minutes=max(1, math.ceil(total_users / 50)),  # ~50 users/minute
            migration_types=[
                MigrationType.LEGACY_TO_PLATFORM,
                MigrationType.ENHANCED_TO_PLATFORM,
                MigrationType.MERGE_DUPLICATES,
                MigrationType.DATA_CLEANUP
            ],
            pre_migration_checks=[
                "✅ Create backup of existing user tables",
                "✅ Validate data integrity constraints",
                "✅ Ensure Redis cache is available",
                "✅ Verify database connection pool capacity",
                "✅ Create migration tracking table"
            ],
            migration_steps=[
                "🔄 Migrate legacy users in batches",
                "🔄 Migrate enhanced user data",
                "🔄 Create school memberships",
                "🔄 Merge duplicate accounts",
                "🔄 Validate migrated data integrity",
                "🔄 Update foreign key references",
                "🔄 Clean up temporary data"
            ],
            post_migration_validation=[
                "✅ Verify user count matches source",
                "✅ Validate school membership integrity",
                "✅ Test authentication flows",
                "✅ Verify API endpoint functionality",
                "✅ Performance benchmark comparison"
            ],
            rollback_plan=[
                "💾 Restore tables from backup",
                "🔧 Revert application code changes",
                "🗑️  Clear all caches",
                "📊 Verify system functionality"
            ],
            risk_assessment={
                "data_loss": "LOW - Full backup created before migration",
                "downtime": "NONE - Zero-downtime migration strategy",
                "performance": "LOW - Batch processing with delays",
                "rollback_complexity": "LOW - Automated rollback available"
            }
        )
        
        self.logger.info(f"Migration plan created: {plan.batches} batches, ~{plan.estimated_duration_minutes} minutes")
        return plan
    
    # Migration Execution
    
    async def run_full_migration(self, dry_run: bool = False) -> Dict[str, Any]:
        """
        Execute complete migration process
        
        Args:
            dry_run: If True, simulate migration without making changes
        """
        start_time = datetime.utcnow()
        self.logger.info(f"Starting {'DRY RUN' if dry_run else 'FULL'} migration...")
        
        try:
            # Step 1: Pre-migration validation
            self.logger.info("Step 1: Pre-migration validation")
            validation_result = await self._run_pre_migration_validation()
            if not validation_result["passed"]:
                return {
                    "success": False,
                    "error": "Pre-migration validation failed",
                    "validation_result": validation_result
                }
            
            # Step 2: Create migration plan
            self.logger.info("Step 2: Creating migration plan")
            plan = await self.create_migration_plan()
            
            # Step 3: Create backup (if not dry run)
            if not dry_run:
                self.logger.info("Step 3: Creating backup")
                backup_result = await self._create_backup()
                if not backup_result["success"]:
                    return {"success": False, "error": "Backup creation failed", "backup_result": backup_result}
            
            # Step 4: Execute migration batches
            self.logger.info("Step 4: Executing migration batches")
            batch_results = []
            total_migrated = 0
            total_errors = 0
            
            # Migrate legacy users
            if plan.total_users > 0:
                legacy_results = await self._migrate_legacy_users_batched(dry_run)
                batch_results.extend(legacy_results)
                
                for result in legacy_results:
                    total_migrated += result.migrated_count
                    total_errors += result.error_count
            
            # Step 5: Post-migration validation
            self.logger.info("Step 5: Post-migration validation")
            if not dry_run:
                post_validation = await self._run_post_migration_validation()
            else:
                post_validation = {"passed": True, "checks": ["DRY RUN - Validation skipped"]}
            
            # Step 6: Clear caches
            if not dry_run:
                self.logger.info("Step 6: Clearing caches")
                await self._clear_all_caches()
            
            end_time = datetime.utcnow()
            duration = (end_time - start_time).total_seconds()
            
            result = {
                "success": True,
                "dry_run": dry_run,
                "migration_plan": plan.__dict__,
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "duration_seconds": duration,
                "total_users_migrated": total_migrated,
                "total_errors": total_errors,
                "success_rate": total_migrated / (total_migrated + total_errors) if (total_migrated + total_errors) > 0 else 1.0,
                "batch_results": [result.__dict__ for result in batch_results],
                "pre_validation": validation_result,
                "post_validation": post_validation
            }
            
            self.logger.info(f"Migration completed successfully in {duration:.2f} seconds")
            return result
            
        except Exception as e:
            self.logger.error(f"Migration failed with error: {e}")
            return {
                "success": False,
                "error": str(e),
                "duration_seconds": (datetime.utcnow() - start_time).total_seconds()
            }
    
    async def _migrate_legacy_users_batched(self, dry_run: bool = False) -> List[MigrationBatchResult]:
        """Migrate legacy users in batches"""
        try:
            # Get total count
            count_query = text("SELECT COUNT(*) FROM platform.users")
            result = await self.db.execute(count_query)
            total_users = result.scalar() or 0
            
            if total_users == 0:
                self.logger.info("No legacy users found to migrate")
                return []
            
            batches = math.ceil(total_users / self.batch_size)
            batch_results = []
            
            self.logger.info(f"Migrating {total_users} legacy users in {batches} batches")
            
            for batch_num in range(batches):
                self.logger.info(f"Processing batch {batch_num + 1}/{batches}")
                
                batch_result = await self._migrate_legacy_users_batch(batch_num, dry_run)
                batch_results.append(batch_result)
                
                # Brief pause between batches
                if batch_num < batches - 1:
                    await asyncio.sleep(self.batch_delay_seconds)
            
            return batch_results
            
        except Exception as e:
            self.logger.error(f"Error in batched migration: {e}")
            raise
    
    async def _migrate_legacy_users_batch(self, batch_number: int, dry_run: bool = False) -> MigrationBatchResult:
        """Migrate a single batch of legacy users"""
        start_time = datetime.utcnow()
        offset = batch_number * self.batch_size
        
        try:
            # Get batch of legacy users
            query = text("""
                SELECT id, email, first_name, last_name, school_id, role, 
                       clerk_user_id, is_active, created_at, updated_at, last_login,
                       user_metadata, preferences
                FROM platform.users
                ORDER BY created_at
                LIMIT :limit OFFSET :offset
            """)
            
            result = await self.db.execute(query, {"limit": self.batch_size, "offset": offset})
            legacy_users = result.fetchall()
            
            migrated_count = 0
            errors = []
            
            if not dry_run:
                for user_row in legacy_users:
                    try:
                        # Convert legacy user to new format
                        new_user = await self._convert_legacy_user_to_platform(user_row)
                        
                        # Save to database
                        self.db.add(new_user)
                        await self.db.flush()  # Get ID without committing
                        
                        # Create school membership if applicable
                        if user_row.school_id:
                            membership = await self._create_school_membership_from_legacy(
                                new_user.id, user_row
                            )
                            if membership:
                                self.db.add(membership)
                        
                        # Track migration
                        await self._track_migration(user_row.id, new_user.id, batch_number)
                        
                        migrated_count += 1
                        
                    except Exception as e:
                        errors.append({
                            "user_id": str(user_row.id),
                            "email": user_row.email,
                            "error": str(e)
                        })
                
                # Commit the batch
                await self.db.commit()
            else:
                # Dry run - just count what would be migrated
                migrated_count = len(legacy_users)
            
            end_time = datetime.utcnow()
            duration = (end_time - start_time).total_seconds()
            
            return MigrationBatchResult(
                batch_number=batch_number,
                migration_type=MigrationType.LEGACY_TO_PLATFORM,
                total_records=len(legacy_users),
                migrated_count=migrated_count,
                error_count=len(errors),
                skipped_count=0,
                success_rate=migrated_count / len(legacy_users) if legacy_users else 1.0,
                errors=errors,
                duration_seconds=duration,
                started_at=start_time,
                completed_at=end_time
            )
            
        except Exception as e:
            end_time = datetime.utcnow()
            duration = (end_time - start_time).total_seconds()
            
            self.logger.error(f"Batch {batch_number} failed: {e}")
            
            return MigrationBatchResult(
                batch_number=batch_number,
                migration_type=MigrationType.LEGACY_TO_PLATFORM,
                total_records=0,
                migrated_count=0,
                error_count=1,
                skipped_count=0,
                success_rate=0.0,
                errors=[{"batch_error": str(e)}],
                duration_seconds=duration,
                started_at=start_time,
                completed_at=end_time
            )
    
    async def _convert_legacy_user_to_platform(self, legacy_user) -> PlatformUserDB:
        """Convert legacy user record to new PlatformUser format"""
        # Create profile from legacy metadata
        profile_data = {}
        if legacy_user.user_metadata:
            # Extract profile information from metadata
            metadata = legacy_user.user_metadata if isinstance(legacy_user.user_metadata, dict) else {}
            profile_data = {
                "phone_number": metadata.get("phone"),
                "profile_image_url": metadata.get("avatar_url"),
                "preferred_language": metadata.get("language", "en"),
                "timezone": metadata.get("timezone", "Africa/Harare")
            }
        
        # Create Clerk integration if clerk_user_id exists
        clerk_integration = {}
        if legacy_user.clerk_user_id:
            clerk_integration = {
                "clerk_user_id": legacy_user.clerk_user_id,
                "last_sync": datetime.utcnow().isoformat(),
                "sync_enabled": True
            }
        
        # Map legacy role to platform role
        platform_role = self._map_legacy_role_to_platform(legacy_user.role)
        
        # Create new platform user
        new_user = PlatformUserDB(
            id=legacy_user.id,  # Keep same ID for referential integrity
            email=legacy_user.email.lower().strip(),
            first_name=legacy_user.first_name,
            last_name=legacy_user.last_name,
            platform_role=platform_role.value,
            status=UserStatus.ACTIVE.value if legacy_user.is_active else UserStatus.INACTIVE.value,
            primary_school_id=legacy_user.school_id,
            profile=profile_data,
            clerk_integration=clerk_integration,
            user_preferences=legacy_user.preferences or {},
            created_at=legacy_user.created_at,
            updated_at=legacy_user.updated_at or datetime.utcnow(),
            last_login=legacy_user.last_login
        )
        
        return new_user
    
    def _map_legacy_role_to_platform(self, legacy_role: str) -> PlatformRole:
        """Map legacy role to new platform role"""
        role_mapping = {
            "platform_admin": PlatformRole.SUPER_ADMIN,
            "admin": PlatformRole.SCHOOL_ADMIN,
            "school_admin": PlatformRole.SCHOOL_ADMIN,
            "teacher": PlatformRole.TEACHER,
            "student": PlatformRole.STUDENT,
            "parent": PlatformRole.PARENT,
            "staff": PlatformRole.STAFF,
            "registrar": PlatformRole.REGISTRAR
        }
        
        return role_mapping.get(legacy_role, PlatformRole.STUDENT)
    
    async def _create_school_membership_from_legacy(self, user_id: UUID, legacy_user) -> Optional[SchoolMembershipDB]:
        """Create school membership from legacy user data"""
        if not legacy_user.school_id:
            return None
        
        try:
            # Get school information
            school_query = text("""
                SELECT name, subdomain FROM platform.schools WHERE id = :school_id
            """)
            result = await self.db.execute(school_query, {"school_id": legacy_user.school_id})
            school_row = result.first()
            
            if not school_row:
                self.logger.warning(f"School {legacy_user.school_id} not found for user {user_id}")
                return None
            
            # Map role to school role
            school_role = self._map_legacy_role_to_school(legacy_user.role)
            
            # Create membership
            membership = SchoolMembershipDB(
                user_id=user_id,
                school_id=legacy_user.school_id,
                school_name=school_row.name,
                school_subdomain=school_row.subdomain,
                role=school_role.value,
                joined_date=legacy_user.created_at,
                status=UserStatus.ACTIVE.value if legacy_user.is_active else UserStatus.INACTIVE.value,
                permissions=self._get_default_permissions_for_role(school_role)
            )
            
            return membership
            
        except Exception as e:
            self.logger.error(f"Error creating school membership for user {user_id}: {e}")
            return None
    
    def _map_legacy_role_to_school(self, legacy_role: str) -> SchoolRole:
        """Map legacy role to school role"""
        role_mapping = {
            "admin": SchoolRole.PRINCIPAL,
            "school_admin": SchoolRole.PRINCIPAL,
            "teacher": SchoolRole.TEACHER,
            "student": SchoolRole.STUDENT,
            "parent": SchoolRole.PARENT,
            "registrar": SchoolRole.REGISTRAR,
            "staff": SchoolRole.TEACHER  # Default staff to teacher
        }
        
        return role_mapping.get(legacy_role, SchoolRole.STUDENT)
    
    def _get_default_permissions_for_role(self, role: SchoolRole) -> List[str]:
        """Get default permissions for a school role"""
        permissions_map = {
            SchoolRole.PRINCIPAL: ["*"],  # All permissions
            SchoolRole.DEPUTY_PRINCIPAL: ["students.*", "staff.read", "reports.*"],
            SchoolRole.TEACHER: ["students.read", "attendance.*", "grades.*"],
            SchoolRole.REGISTRAR: ["students.*", "documents.*"],
            SchoolRole.STUDENT: ["profile.read", "assignments.view"],
            SchoolRole.PARENT: ["children.read", "payments.*"]
        }
        
        return permissions_map.get(role, [])
    
    async def _track_migration(self, legacy_user_id: UUID, new_user_id: UUID, batch_number: int):
        """Track migration for rollback purposes"""
        query = text("""
            INSERT INTO platform.user_migration_tracking 
            (legacy_user_id, new_user_id, migration_type, batch_number, migrated_at)
            VALUES (:legacy_user_id, :new_user_id, :migration_type, :batch_number, :migrated_at)
        """)
        
        await self.db.execute(query, {
            "legacy_user_id": legacy_user_id,
            "new_user_id": new_user_id,
            "migration_type": MigrationType.LEGACY_TO_PLATFORM.value,
            "batch_number": batch_number,
            "migrated_at": datetime.utcnow()
        })
    
    # Validation Methods
    
    async def _run_pre_migration_validation(self) -> Dict[str, Any]:
        """Run pre-migration validation checks"""
        checks = []
        passed = True
        
        try:
            # Check 1: Database connectivity
            await self.db.execute(text("SELECT 1"))
            checks.append({"name": "Database connectivity", "status": "PASSED"})
        except Exception as e:
            checks.append({"name": "Database connectivity", "status": "FAILED", "error": str(e)})
            passed = False
        
        # Check 2: Required tables exist
        try:
            await self.db.execute(text("SELECT 1 FROM platform.platform_users LIMIT 1"))
            checks.append({"name": "Target tables exist", "status": "PASSED"})
        except Exception:
            checks.append({"name": "Target tables exist", "status": "FAILED"})
            passed = False
        
        # Check 3: Cache connectivity
        try:
            await self.cache.health_check()
            checks.append({"name": "Cache connectivity", "status": "PASSED"})
        except Exception as e:
            checks.append({"name": "Cache connectivity", "status": "FAILED", "error": str(e)})
            # Cache failure is not critical for migration
        
        return {"passed": passed, "checks": checks}
    
    async def _run_post_migration_validation(self) -> Dict[str, Any]:
        """Run post-migration validation checks"""
        checks = []
        passed = True
        
        # Check 1: User count validation
        try:
            legacy_count_query = text("SELECT COUNT(*) FROM platform.users")
            legacy_result = await self.db.execute(legacy_count_query)
            legacy_count = legacy_result.scalar()
            
            platform_count_query = text("SELECT COUNT(*) FROM platform.platform_users")
            platform_result = await self.db.execute(platform_count_query)
            platform_count = platform_result.scalar()
            
            if legacy_count == platform_count:
                checks.append({
                    "name": "User count validation", 
                    "status": "PASSED",
                    "details": f"Legacy: {legacy_count}, Platform: {platform_count}"
                })
            else:
                checks.append({
                    "name": "User count validation", 
                    "status": "FAILED",
                    "details": f"Legacy: {legacy_count}, Platform: {platform_count}"
                })
                passed = False
                
        except Exception as e:
            checks.append({"name": "User count validation", "status": "ERROR", "error": str(e)})
            passed = False
        
        # Check 2: School membership validation
        try:
            membership_query = text("""
                SELECT COUNT(*) FROM platform.school_memberships sm
                JOIN platform.platform_users pu ON sm.user_id = pu.id
                WHERE pu.primary_school_id = sm.school_id
            """)
            result = await self.db.execute(membership_query)
            valid_memberships = result.scalar()
            
            checks.append({
                "name": "School membership validation",
                "status": "PASSED",
                "details": f"Valid memberships: {valid_memberships}"
            })
            
        except Exception as e:
            checks.append({"name": "School membership validation", "status": "ERROR", "error": str(e)})
        
        return {"passed": passed, "checks": checks}
    
    # Utility Methods
    
    async def _create_backup(self) -> Dict[str, Any]:
        """Create backup of existing tables"""
        # This would implement actual backup logic
        # For now, return success
        return {"success": True, "backup_location": f"backup_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"}
    
    async def _clear_all_caches(self):
        """Clear all relevant caches after migration"""
        try:
            cache_stats = await self.cache.get_cache_stats()
            total_keys = cache_stats.get('total_keys', 0)
            
            if total_keys > 0:
                # Clear all 1class keys
                await self.cache.delete_pattern("oneclass:*")
                self.logger.info(f"Cleared {total_keys} cache keys")
        except Exception as e:
            self.logger.warning(f"Error clearing caches: {e}")
    
    # Rollback Methods
    
    async def rollback_migration(self, backup_location: str) -> Dict[str, Any]:
        """Rollback migration to previous state"""
        self.logger.warning("ROLLBACK: Starting migration rollback")
        
        try:
            # This would implement actual rollback logic
            # 1. Restore from backup
            # 2. Clear new tables
            # 3. Reset foreign keys
            # 4. Clear caches
            
            await self._clear_all_caches()
            
            return {
                "success": True,
                "message": "Migration rollback completed",
                "backup_restored": backup_location
            }
            
        except Exception as e:
            self.logger.error(f"Rollback failed: {e}")
            return {"success": False, "error": str(e)}