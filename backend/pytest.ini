[pytest]
# Pytest configuration for OneClass Platform
minversion = 6.0
testpaths = 
    services/finance/tests
    tests

# Test discovery patterns
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    slow: Slow running tests
    paynow: Tests requiring Paynow integration
    database: Tests requiring database
    api: API endpoint tests
    performance: Performance tests

# Coverage settings
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --durations=10
    --cov=services/finance
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-report=xml:coverage.xml
    --cov-fail-under=80
    --cov-branch
    --no-cov-on-fail

# Test timeout
timeout = 300
timeout_method = thread

# Filtering
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore:.*urllib3.*:DeprecationWarning

# Asyncio settings
asyncio_mode = auto

# Logging
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Test environments
env =
    TEST_DATABASE_URL = postgresql+asyncpg://test:test@localhost:5432/finance_test
    ENVIRONMENT = test
    PAYNOW_INTEGRATION_ID = test_integration_id
    PAYNOW_INTEGRATION_KEY = test_integration_key
    PAYNOW_RETURN_URL = https://test.oneclass.zw/payment/return
    PAYNOW_RESULT_URL = https://test.oneclass.zw/payment/webhook