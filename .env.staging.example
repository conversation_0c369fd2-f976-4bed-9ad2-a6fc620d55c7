# OneClass Platform - Staging Environment Configuration
# Copy this file to .env.staging and fill in your actual staging values
# Use staging values that mirror production but with test data

# ================================================
# CORE APPLICATION SETTINGS
# ================================================

# Environment
NODE_ENV=staging
ENVIRONMENT=staging
DEBUG=false

# Application URLs
NEXT_PUBLIC_API_URL=https://api-staging.oneclass.ac.zw
NEXT_PUBLIC_APP_URL=https://app-staging.oneclass.ac.zw
API_BASE_URL=https://api-staging.oneclass.ac.zw

# Domain Configuration
NEXT_PUBLIC_DEFAULT_DOMAIN=staging.oneclass.ac.zw
PRODUCTION_DOMAIN=staging.oneclass.ac.zw
ENABLE_MULTITENANCY=true
CUSTOM_DOMAINS_ENABLED=true

# ================================================
# AUTHENTICATION & SECURITY
# ================================================

# Clerk Authentication (Staging Keys)
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_your-staging-publishable-key
CLERK_SECRET_KEY=sk_test_your-staging-secret-key
CLERK_WEBHOOK_SECRET=whsec_your-staging-webhook-secret

# JWT Configuration
JWT_SECRET=your-secure-staging-jwt-secret-256-bits-minimum
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
JWT_ALGORITHM=HS256

# NextAuth Configuration
NEXTAUTH_SECRET=your-secure-nextauth-staging-secret
NEXTAUTH_URL=https://app-staging.oneclass.ac.zw

# Session Configuration
SESSION_SECRET=your-session-secret-key-staging
SESSION_TIMEOUT=86400
SESSION_SECURE=true
SESSION_SAME_SITE=strict

# CORS Configuration
CORS_ORIGINS=https://app-staging.oneclass.ac.zw,https://admin-staging.oneclass.ac.zw,https://*.staging.oneclass.ac.zw
CORS_CREDENTIALS=true

# ================================================
# DATABASE CONFIGURATION
# ================================================

# PostgreSQL Staging Database
DATABASE_URL=postgresql://staging_user:<EMAIL>:5432/oneclass_staging
DB_HOST=staging-db.oneclass.ac.zw
DB_PORT=5432
DB_NAME=oneclass_staging
DB_USER=staging_user
DB_PASSWORD=staging_password
DB_SSL=require
DB_POOL_SIZE=10
DB_MAX_CONNECTIONS=50
DB_CONNECTION_TIMEOUT=30000

# Database Migrations
RUN_MIGRATIONS=true
MIGRATIONS_TABLE=schema_migrations

# ================================================
# REDIS CONFIGURATION
# ================================================

# Redis for Caching and Sessions
REDIS_URL=redis://staging-redis.oneclass.ac.zw:6379
REDIS_HOST=staging-redis.oneclass.ac.zw
REDIS_PORT=6379
REDIS_PASSWORD=staging_redis_password
REDIS_DB=0
REDIS_TLS=true

# Cache Configuration
CACHE_TTL=1800
CACHE_MAX_SIZE=500
SESSION_STORE=redis

# ================================================
# FILE STORAGE
# ================================================

# AWS S3 Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=AKIA_your_staging_access_key
AWS_SECRET_ACCESS_KEY=your_staging_secret_access_key
AWS_S3_BUCKET=oneclass-staging-files
AWS_S3_REGION=us-east-1
AWS_CLOUDFRONT_DOMAIN=cdn-staging.oneclass.ac.zw

# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=pdf,doc,docx,xls,xlsx,jpg,jpeg,png,gif
FILE_ENCRYPTION=true

# ================================================
# EMAIL CONFIGURATION
# ================================================

# SMTP Configuration (use test service for staging)
SMTP_HOST=smtp.mailtrap.io
SMTP_PORT=587
SMTP_SECURE=true
SMTP_USER=your_mailtrap_username
SMTP_PASSWORD=your_mailtrap_password
SMTP_FROM_NAME=OneClass Platform (Staging)
SMTP_FROM_EMAIL=<EMAIL>

# Email Templates
EMAIL_TEMPLATES_ENABLED=true
EMAIL_TEMPLATE_PATH=/app/templates/email

# ================================================
# EXTERNAL SERVICES
# ================================================

# Paynow Payment Gateway (Test Environment)
PAYNOW_INTEGRATION_ID=your_test_paynow_id
PAYNOW_INTEGRATION_KEY=your_test_paynow_key
PAYNOW_RETURN_URL=https://app-staging.oneclass.ac.zw/payments/return
PAYNOW_RESULT_URL=https://api-staging.oneclass.ac.zw/webhooks/paynow

# OpenAI API (for AI features)
OPENAI_API_KEY=sk-your-staging-openai-api-key
OPENAI_ORG_ID=org-your-openai-organization-id
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=1000

# SMS Service (test numbers)
TWILIO_ACCOUNT_SID=ACyour_twilio_test_account_sid
TWILIO_AUTH_TOKEN=your_twilio_test_auth_token
TWILIO_PHONE_NUMBER=+***********

# ================================================
# MONITORING & LOGGING
# ================================================

# Application Performance Monitoring
SENTRY_DSN=https://<EMAIL>/staging-project-id
SENTRY_ENVIRONMENT=staging
SENTRY_TRACES_SAMPLE_RATE=1.0
SENTRY_PROFILES_SAMPLE_RATE=1.0

# Logging Configuration
LOG_LEVEL=debug
LOG_FORMAT=json
LOG_DESTINATION=console
LOG_FILE_PATH=/var/log/oneclass/staging.log

# ================================================
# RATE LIMITING
# ================================================

# Rate Limiting Configuration (more lenient for testing)
RATE_LIMIT_ENABLED=true
RATE_LIMIT_WINDOW=15m
RATE_LIMIT_MAX_REQUESTS=2000
RATE_LIMIT_SKIP_TRUSTED=true

# API Rate Limits
API_RATE_LIMIT_PER_MINUTE=120
AUTH_RATE_LIMIT_PER_MINUTE=10
UPLOAD_RATE_LIMIT_PER_MINUTE=20

# ================================================
# BACKUP & DISASTER RECOVERY
# ================================================

# Database Backup (daily for staging)
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 4 * * *
BACKUP_RETENTION_DAYS=7
BACKUP_S3_BUCKET=oneclass-staging-backups
BACKUP_ENCRYPTION_KEY=your_staging_backup_encryption_key

# ================================================
# FEATURE FLAGS
# ================================================

# Feature Flags (enable all for testing)
FEATURE_AI_ASSISTANCE=true
FEATURE_ADVANCED_REPORTING=true
FEATURE_MOBILE_APP=true
FEATURE_BULK_OPERATIONS=true
FEATURE_CUSTOM_DOMAINS=true
FEATURE_SSO=true
FEATURE_API_ACCESS=true

# Experimental Features (test environment)
FEATURE_BETA_DASHBOARD=true
FEATURE_ML_INSIGHTS=true
FEATURE_BLOCKCHAIN_CERTIFICATES=false

# ================================================
# ANALYTICS & TRACKING
# ================================================

# Google Analytics (staging property)
NEXT_PUBLIC_GA_TRACKING_ID=GA-STAGING-XXX
GOOGLE_ANALYTICS_PROPERTY_ID=your_staging_ga_property_id

# Custom Analytics
ANALYTICS_ENABLED=true
ANALYTICS_ENDPOINT=https://analytics-staging.oneclass.ac.zw

# ================================================
# HEALTH CHECKS & UPTIME
# ================================================

# Health Check Configuration
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=5
HEALTH_CHECK_ENDPOINT=/health

# ================================================
# TIMEZONE & LOCALIZATION
# ================================================

# Timezone
TZ=Africa/Harare
DEFAULT_TIMEZONE=Africa/Harare

# Localization
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=en,sn,nd
CURRENCY_CODE=USD
COUNTRY_CODE=ZW

# ================================================
# MAINTENANCE MODE
# ================================================

# Maintenance
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE=OneClass Platform (Staging) is currently under maintenance.
MAINTENANCE_ALLOWED_IPS=127.0.0.1,::1

# ================================================
# WEBHOOK CONFIGURATION
# ================================================

# Webhook Settings
WEBHOOK_SECRET=your_staging_webhook_secret_key
WEBHOOK_TIMEOUT=30000
WEBHOOK_RETRY_ATTEMPTS=3

# Third-party Webhooks
CLERK_WEBHOOK_SECRET=whsec_your_staging_clerk_webhook_secret
STRIPE_WEBHOOK_SECRET=whsec_your_staging_stripe_webhook_secret
PAYNOW_WEBHOOK_SECRET=your_staging_paynow_webhook_secret