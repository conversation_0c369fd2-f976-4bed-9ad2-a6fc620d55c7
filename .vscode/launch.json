{"version": "0.2.0", "configurations": [{"name": "Python: FastAPI", "type": "python", "request": "launch", "module": "u<PERSON><PERSON>", "args": ["shared.main:app", "--reload", "--port", "8000"], "cwd": "${workspaceFolder}/backend", "env": {"PYTHONPATH": "${workspaceFolder}/backend"}, "console": "integratedTerminal", "justMyCode": true}, {"name": "Python: Current File", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "justMyCode": true}, {"name": "Debug Frontend", "type": "chrome", "request": "launch", "url": "http://localhost:3000", "webRoot": "${workspaceFolder}/frontend/src", "sourceMapPathOverrides": {"webpack:///./src/*": "${webRoot}/*"}}]}