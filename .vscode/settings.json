{"python.defaultInterpreterPath": "./backend/.venv/bin/python", "python.formatting.provider": "black", "python.linting.enabled": true, "python.linting.flake8Enabled": true, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}, "typescript.preferences.importModuleSpecifier": "relative", "tailwindCSS.experimental.classRegex": [["cn\\(([^)]*)\\)", "'([^']*)'"], ["className\\s*:\\s*['\"`]([^'\"`]*)['\"`]"]], "files.associations": {"*.sql": "sql"}}