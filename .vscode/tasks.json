{"version": "2.0.0", "tasks": [{"label": "Sync Wiki", "type": "shell", "command": "./tools/scripts/sync-wiki.sh", "group": {"kind": "build", "isDefault": false}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Start Backend", "type": "shell", "command": "cd backend && source .venv/bin/activate && python -m uvicorn shared.main:app --reload --port 8000", "group": "none", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "dedicated", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Start Frontend", "type": "shell", "command": "cd frontend && npm run dev", "group": "none", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "dedicated", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Start Databases", "type": "shell", "command": "docker-compose up -d postgres redis", "group": "none", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Run Tests", "type": "shell", "command": "cd backend && source .venv/bin/activate && pytest", "group": {"kind": "test", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}]}