# OneClass Platform: Deployment & Integration Guide

## Overview

This guide provides step-by-step instructions for deploying the consolidated user system to production and integrating all components of the OneClass platform.

## Prerequisites

### System Requirements
- **PostgreSQL 14+** with UUID extension
- **Redis 7+** for caching
- **Python 3.9+** with FastAPI
- **Docker & Docker Compose** (optional)
- **4GB+ RAM** minimum
- **SSD storage** recommended for database

### Environment Setup
```bash
# Clone repository
git clone https://github.com/oneclass/oneclass-platform.git
cd oneclass-platform

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

## Database Setup

### 1. Create Database
```sql
-- Connect to PostgreSQL as superuser
CREATE DATABASE oneclass;
CREATE USER oneclass_app WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE oneclass TO oneclass_app;

-- Connect to oneclass database
\c oneclass;

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "btree_gin";
```

### 2. Apply Database Schemas
```bash
# Apply schemas in order
psql -d oneclass -f backend/database/schemas/01_platform_schema.sql
psql -d oneclass -f backend/database/schemas/02_schools.sql
psql -d oneclass -f backend/database/schemas/03_academic.sql
psql -d oneclass -f backend/database/schemas/04_finance.sql
psql -d oneclass -f backend/database/schemas/05_migration_services.sql
psql -d oneclass -f backend/database/schemas/06_monitoring.sql
psql -d oneclass -f backend/database/schemas/07_platform_users.sql  # New consolidated schema
```

### 3. Create Application User and Permissions
```sql
-- Grant permissions to application user
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA platform TO oneclass_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA academic TO oneclass_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA finance TO oneclass_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA migration_services TO oneclass_app;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA platform TO oneclass_app;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA academic TO oneclass_app;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA finance TO oneclass_app;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA migration_services TO oneclass_app;
```

## Redis Setup

### 1. Install and Configure Redis
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install redis-server

# Configure Redis for production
sudo nano /etc/redis/redis.conf
```

### 2. Redis Configuration
```conf
# /etc/redis/redis.conf
bind 127.0.0.1
port 6379
maxmemory 2gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

### 3. Start Redis
```bash
sudo systemctl start redis-server
sudo systemctl enable redis-server

# Test connection
redis-cli ping  # Should return PONG
```

## Environment Configuration

### 1. Create Environment File
```bash
# Create .env file
cp .env.example .env
nano .env
```

### 2. Environment Variables
```bash
# .env file
# Database Configuration
DATABASE_URL=postgresql://oneclass_app:your_secure_password@localhost:5432/oneclass
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here
JWT_ALGORITHM=HS256
JWT_EXPIRE_HOURS=24

# Clerk Integration (optional)
CLERK_API_KEY=your_clerk_api_key
CLERK_SECRET_KEY=your_clerk_secret_key

# Domain Configuration
DOMAIN=oneclass.ac.zw
API_DOMAIN=api.oneclass.ac.zw
FRONTEND_DOMAIN=app.oneclass.ac.zw

# Cache Configuration
CACHE_TTL_USER_CONTEXT=300
CACHE_TTL_SCHOOL_INFO=900
CACHE_TTL_MINIMAL_CONTEXT=120

# Performance Monitoring
MONITORING_ENABLED=true
METRICS_RETENTION_HOURS=168  # 7 days
ALERT_EMAIL=<EMAIL>

# Zimbabwe-specific Configuration
DEFAULT_TIMEZONE=Africa/Harare
DEFAULT_CURRENCY=USD
BACKUP_CURRENCY=ZWL
PHONE_COUNTRY_CODE=+263

# Feature Flags
ENABLE_MIGRATION_SERVICES=true
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_BULK_USER_IMPORT=true
```

## Application Deployment

### 1. Initialize Services
```python
# backend/main.py
import asyncio
import logging
from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import redis.asyncio as redis

# Import our services
from shared.cache.user_context_cache import UserContextCache
from shared.services.optimized_user_service import OptimizedUserService
from shared.middleware.fast_user_context_middleware import FastUserContextHTTPMiddleware
from shared.services.performance_monitoring_service import PerformanceMonitoringService
from shared.services.user_migration_service import UserMigrationService

# Global service instances
cache_service = None
user_service = None
monitoring_service = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    global cache_service, user_service, monitoring_service
    
    # Initialize Redis
    redis_client = redis.from_url(
        os.getenv("REDIS_URL", "redis://localhost:6379"),
        encoding="utf-8",
        decode_responses=True
    )
    
    # Initialize cache service
    cache_service = UserContextCache(redis_client)
    
    # Initialize database session
    from shared.database import get_async_session
    async with get_async_session() as db_session:
        # Initialize user service
        user_service = OptimizedUserService(db_session, cache_service)
        
        # Initialize monitoring service
        monitoring_service = PerformanceMonitoringService(
            cache_service, user_service, redis_client
        )
        
        # Start monitoring
        await monitoring_service.start_monitoring()
    
    # Application startup complete
    logging.info("OneClass platform started successfully")
    
    yield
    
    # Shutdown
    await monitoring_service.stop_monitoring()
    await redis_client.close()
    logging.info("OneClass platform shutdown complete")

# Create FastAPI app
app = FastAPI(
    title="OneClass Platform API",
    description="Comprehensive school management platform for Zimbabwe",
    version="2.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://app.oneclass.ac.zw", "https://oneclass.ac.zw"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add user context middleware
app.add_middleware(
    FastUserContextHTTPMiddleware,
    cache=cache_service,
    user_service=user_service
)

# Include routers
from services.user_management.routes import router as user_router
from services.migration_services.routes import router as migration_router
from services.finance.main import finance_app

app.include_router(user_router, prefix="/api/v1")
app.include_router(migration_router, prefix="/api/v1")
app.mount("/api/v1/finance", finance_app)

@app.get("/health")
async def health_check():
    """Application health check"""
    health_status = await monitoring_service.health_check()
    return health_status

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=False,  # Set to True for development
        log_level="info"
    )
```

### 2. Run Database Migration
```python
# scripts/migrate_users.py
import asyncio
import logging
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

from shared.cache.user_context_cache import UserContextCache
from shared.services.user_migration_service import UserMigrationService
import redis.asyncio as redis

async def run_migration():
    """Execute user system migration"""
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    # Initialize database
    engine = create_async_engine(
        os.getenv("DATABASE_URL"),
        echo=True,
        pool_size=10,
        max_overflow=20
    )
    
    AsyncSessionLocal = sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False
    )
    
    # Initialize Redis
    redis_client = redis.from_url(os.getenv("REDIS_URL"))
    cache = UserContextCache(redis_client)
    
    # Run migration
    async with AsyncSessionLocal() as db_session:
        migration_service = UserMigrationService(db_session, cache)
        
        logger.info("Starting user system migration...")
        
        # Analyze existing data
        analysis = await migration_service.analyze_existing_data()
        logger.info(f"Migration analysis: {analysis}")
        
        # Create migration plan
        plan = await migration_service.create_migration_plan()
        logger.info(f"Migration plan: {plan}")
        
        # Execute migration
        result = await migration_service.run_full_migration(dry_run=False)
        
        if result["success"]:
            logger.info(f"Migration completed successfully!")
            logger.info(f"Migrated {result['total_users_migrated']} users")
            logger.info(f"Success rate: {result['success_rate']:.2%}")
        else:
            logger.error(f"Migration failed: {result.get('error')}")
            return False
    
    await redis_client.close()
    return True

if __name__ == "__main__":
    success = asyncio.run(run_migration())
    exit(0 if success else 1)
```

### 3. Start Application
```bash
# Development
python backend/main.py

# Production with Gunicorn
pip install gunicorn[gthread]
gunicorn -w 4 -k uvicorn.workers.UvicornWorker backend.main:app --bind 0.0.0.0:8000

# Or with systemd service
sudo systemctl start oneclass-api
sudo systemctl enable oneclass-api
```

## Docker Deployment (Alternative)

### 1. Docker Compose Configuration
```yaml
# docker-compose.yml
version: '3.8'

services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

  postgres:
    image: postgres:14
    environment:
      POSTGRES_DB: oneclass
      POSTGRES_USER: oneclass_app
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/database/schemas:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"

  api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=postgresql://oneclass_app:${DATABASE_PASSWORD}@postgres:5432/oneclass
      - REDIS_URL=redis://redis:6379/0
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app/backend
    command: uvicorn backend.main:app --host 0.0.0.0 --port 8000

volumes:
  postgres_data:
  redis_data:
```

### 2. Dockerfile
```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    postgresql-client \
    gcc \
    python3-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application
COPY backend ./backend
COPY scripts ./scripts

# Set Python path
ENV PYTHONPATH=/app/backend

# Create non-root user
RUN useradd --create-home --shell /bin/bash app
USER app

EXPOSE 8000

CMD ["uvicorn", "backend.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 3. Deploy with Docker
```bash
# Build and start services
docker-compose up --build -d

# Run migration
docker-compose exec api python scripts/migrate_users.py

# Check logs
docker-compose logs -f api
```

## Monitoring Setup

### 1. Health Check Endpoints
```python
# Add to main.py routes
@app.get("/api/v1/health/detailed")
async def detailed_health_check():
    """Detailed health check with all components"""
    return {
        "database": await check_database_health(),
        "redis": await check_redis_health(),
        "user_service": await user_service.health_check(),
        "cache": await cache_service.health_check(),
        "monitoring": await monitoring_service.health_check()
    }

@app.get("/api/v1/metrics")
async def get_metrics():
    """Get performance metrics"""
    if not monitoring_service:
        raise HTTPException(404, "Monitoring not enabled")
    
    return await monitoring_service.get_performance_report(hours=1)
```

### 2. Alerts Configuration
```python
# Configure alerts in monitoring service
await monitoring_service.configure_alerts({
    "user_context_resolution_time": 100,  # ms
    "cache_hit_rate": 0.80,  # 80%
    "database_query_time": 50,  # ms
    "memory_usage": 80,  # percent
    "error_rate": 0.01,  # 1%
    "disk_usage": 85,  # percent
})
```

## Production Optimizations

### 1. Database Optimizations
```sql
-- Add additional indexes for performance
CREATE INDEX CONCURRENTLY idx_platform_users_email_active 
ON platform.platform_users(email) WHERE status = 'active';

CREATE INDEX CONCURRENTLY idx_school_memberships_user_school_active 
ON platform.school_memberships(user_id, school_id) WHERE status = 'active';

CREATE INDEX CONCURRENTLY idx_school_memberships_school_role_active 
ON platform.school_memberships(school_id, role) WHERE status = 'active';

-- Update table statistics
ANALYZE platform.platform_users;
ANALYZE platform.school_memberships;
```

### 2. Cache Warming
```python
# scripts/warm_cache.py
async def warm_cache():
    """Warm critical cache entries"""
    
    # Warm active schools
    schools = await get_active_schools()
    for school in schools:
        await cache_service.set_school_info(school.id, school.dict())
    
    # Warm frequently accessed users
    active_users = await get_recently_active_users(limit=1000)
    for user in active_users:
        await cache_service.set_user_context(user.id, {'user': user.dict()})
    
    logging.info(f"Cache warmed with {len(schools)} schools and {len(active_users)} users")

# Run cache warming on startup
asyncio.create_task(warm_cache())
```

### 3. Connection Pooling
```python
# Enhanced database configuration
from sqlalchemy.pool import QueuePool

engine = create_async_engine(
    DATABASE_URL,
    poolclass=QueuePool,
    pool_size=20,
    max_overflow=30,
    pool_pre_ping=True,
    pool_recycle=3600,
    echo=False  # Set to True for query debugging
)
```

## Security Configuration

### 1. SSL/TLS Setup
```nginx
# /etc/nginx/sites-available/oneclass
server {
    listen 443 ssl http2;
    server_name api.oneclass.ac.zw;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 2. Rate Limiting
```python
# Add rate limiting middleware
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded

limiter = Limiter(key_func=get_remote_address)
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

@app.get("/api/v1/users/search")
@limiter.limit("50/minute")
async def search_users(request: Request, ...):
    # Search implementation
    pass
```

## Backup and Recovery

### 1. Database Backup
```bash
#!/bin/bash
# scripts/backup_database.sh

BACKUP_DIR="/var/backups/oneclass"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="oneclass"

# Create backup directory
mkdir -p $BACKUP_DIR

# Create database backup
pg_dump -h localhost -U oneclass_app -d $DB_NAME \
  --verbose --format=custom \
  --file="$BACKUP_DIR/oneclass_backup_$DATE.dump"

# Compress backup
gzip "$BACKUP_DIR/oneclass_backup_$DATE.dump"

# Clean old backups (keep 30 days)
find $BACKUP_DIR -name "*.gz" -mtime +30 -delete

echo "Backup completed: oneclass_backup_$DATE.dump.gz"
```

### 2. Redis Backup
```bash
#!/bin/bash
# scripts/backup_redis.sh

BACKUP_DIR="/var/backups/oneclass/redis"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# Save Redis snapshot
redis-cli BGSAVE

# Wait for background save to complete
while [ $(redis-cli LASTSAVE) -eq $(redis-cli LASTSAVE) ]; do
  sleep 1
done

# Copy dump file
cp /var/lib/redis/dump.rdb "$BACKUP_DIR/redis_backup_$DATE.rdb"
gzip "$BACKUP_DIR/redis_backup_$DATE.rdb"

echo "Redis backup completed: redis_backup_$DATE.rdb.gz"
```

### 3. Automated Backup Cron
```cron
# Add to crontab: crontab -e
# Backup database daily at 2 AM
0 2 * * * /path/to/scripts/backup_database.sh

# Backup Redis every 6 hours
0 */6 * * * /path/to/scripts/backup_redis.sh
```

## Testing Deployment

### 1. Run Integration Tests
```bash
# Test database connection
python -c "
import asyncio
from shared.database import test_connection
asyncio.run(test_connection())
"

# Test Redis connection
python -c "
import redis
r = redis.Redis.from_url('redis://localhost:6379')
print('Redis ping:', r.ping())
"

# Test user system
python -c "
import asyncio
from scripts.test_user_system import run_tests
asyncio.run(run_tests())
"
```

### 2. Performance Testing
```bash
# Install testing tools
pip install locust

# Run load tests
locust -f tests/load_tests.py --host=https://api.oneclass.ac.zw
```

### 3. Migration Validation
```bash
# Validate migration results
python scripts/validate_migration.py

# Check data integrity
python scripts/check_data_integrity.py
```

## Troubleshooting

### Common Issues

1. **Database Connection Issues**
   ```bash
   # Check PostgreSQL status
   sudo systemctl status postgresql
   
   # Check connections
   psql -d oneclass -c "SELECT count(*) FROM pg_stat_activity;"
   ```

2. **Redis Connection Issues**
   ```bash
   # Check Redis status
   sudo systemctl status redis-server
   
   # Test connection
   redis-cli ping
   ```

3. **Cache Performance Issues**
   ```python
   # Clear cache if needed
   await cache_service.delete_pattern("oneclass:*")
   
   # Check cache statistics
   stats = await cache_service.get_cache_stats()
   print(f"Cache stats: {stats}")
   ```

4. **Migration Issues**
   ```python
   # Rollback migration if needed
   await migration_service.rollback_migration(backup_location)
   
   # Re-run with dry run first
   result = await migration_service.run_full_migration(dry_run=True)
   ```

## Conclusion

The OneClass platform is now ready for production deployment with:

- ✅ **Consolidated User System** with multi-school support
- ✅ **High-Performance Architecture** with Redis caching
- ✅ **Comprehensive Monitoring** and alerting
- ✅ **Zero-Downtime Migration** capabilities
- ✅ **Enterprise Security** with JWT and RLS
- ✅ **Scalable Infrastructure** supporting 10,000+ users

The platform is optimized for Zimbabwe's educational sector with support for local payment systems, multi-currency handling, and comprehensive school management features.

For ongoing support and monitoring, refer to the performance monitoring dashboard and alert system to ensure optimal platform performance.