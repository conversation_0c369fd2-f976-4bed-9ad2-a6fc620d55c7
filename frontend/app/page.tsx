"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  GraduationCap,
  Users,
  BookOpen,
  DollarSign,
  Shield,
  Globe,
  CheckCircle,
  Star,
  ArrowRight,
  Play,
  Menu,
  X,
  Calendar,
  MessageSquare,
  Bus,
  ClipboardCheck,
  BarChart3,
  Building2,
  Heart,
  FileText,
  UserCheck,
  Bell,
  Smartphone,
  Lock,
  Database,
  Zap,
  Award,
  Clock,
  Map,
  Utensils,
  Activity,
  Library,
  Video,
  Briefcase,
  CreditCard,
  TrendingUp,
  UserCog,
  Mail,
  CalendarCheck,
  BookCheck,
  Users2,
  Stethoscope,
  Receipt,
  GraduationCap as Graduation,
  Layers,
  Settings,
  HelpCircle,
  Cloud,
  Wifi,
} from "lucide-react"
import Link from "next/link"

export default function LandingPage() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [showAllFeatures, setShowAllFeatures] = useState(false)

  const coreFeatures = [
    {
      icon: Users,
      title: "Student Information System",
      description: "Complete student profiles, enrollment management, and academic tracking with detailed records",
    },
    {
      icon: BookOpen,
      title: "Academic Management",
      description: "Comprehensive curriculum planning, lesson management, and assessment creation tools",
    },
    {
      icon: ClipboardCheck,
      title: "Attendance Tracking",
      description: "Digital attendance with biometric integration, automated notifications, and absence reports",
    },
    {
      icon: DollarSign,
      title: "Finance & Billing",
      description: "Automated fee collection, payment tracking, invoicing, and comprehensive financial reporting",
    },
    {
      icon: MessageSquare,
      title: "Communication Hub",
      description: "Instant messaging, announcements, parent-teacher communication, and notification system",
    },
    {
      icon: BarChart3,
      title: "Advanced Analytics",
      description: "Comprehensive dashboards, performance insights, predictive analytics, and custom reports",
    },
    {
      icon: GraduationCap,
      title: "AI-Powered Learning",
      description: "Adaptive learning paths, intelligent tutoring, performance predictions, and personalized content",
    },
    {
      icon: Globe,
      title: "Ministry Integration",
      description: "Direct reporting to Ministry of Education with automated compliance and data submission",
    },
    {
      icon: Smartphone,
      title: "Mobile App Suite",
      description: "Dedicated apps for students, parents, teachers, and administrators with offline capabilities",
    },
    {
      icon: Shield,
      title: "Safety & Security",
      description: "Student safety records, incident reporting, emergency procedures, and compliance tracking",
    },
    {
      icon: Bus,
      title: "Transport Management",
      description: "Route planning, vehicle tracking, driver management, and parent notifications",
    },
    {
      icon: Cloud,
      title: "Cloud Infrastructure",
      description: "99.9% uptime, automatic scaling, disaster recovery, and global data centers",
    },
  ]

  const additionalFeatures = [
    {
      icon: Calendar,
      title: "Timetable Management",
      description: "Smart scheduling system with conflict detection and automated timetable generation",
    },
    {
      icon: BookCheck,
      title: "Grade Management",
      description: "Comprehensive grading system with rubrics, weighted assessments, and progress tracking",
    },
    {
      icon: Award,
      title: "Assessment Tools",
      description: "Online exams, automated marking, plagiarism detection, and detailed analytics",
    },
    {
      icon: CreditCard,
      title: "Payment Gateway",
      description: "Multiple payment methods including mobile money, bank transfers, and international options",
    },
    {
      icon: Receipt,
      title: "Fee Management",
      description: "Flexible fee structures, scholarships, payment plans, and automated reminders",
    },
    {
      icon: TrendingUp,
      title: "Financial Analytics",
      description: "Revenue forecasting, expense tracking, budget management, and profitability analysis",
    },
    {
      icon: Bell,
      title: "Smart Notifications",
      description: "Automated alerts for attendance, grades, events, payments, and emergency situations",
    },
    {
      icon: Mail,
      title: "Email Integration",
      description: "Automated email campaigns, newsletters, event invitations, and report distribution",
    },
    {
      icon: UserCheck,
      title: "Staff Management",
      description: "Employee records, payroll integration, performance tracking, and professional development",
    },
    {
      icon: Building2,
      title: "Resource Management",
      description: "Classroom booking, equipment tracking, library management, and facility maintenance",
    },
    {
      icon: Users2,
      title: "Class Management",
      description: "Class formations, student groupings, seating arrangements, and capacity management",
    },
    {
      icon: Briefcase,
      title: "HR & Payroll",
      description: "Staff records, leave management, payroll processing, and compliance tracking",
    },
    {
      icon: Heart,
      title: "Health Management",
      description: "Medical records, vaccination tracking, allergy management, and emergency protocols",
    },
    {
      icon: Stethoscope,
      title: "Medical Center",
      description: "School clinic management, health checkups, medication tracking, and nurse portal",
    },
    {
      icon: Activity,
      title: "Sports & Activities",
      description: "Extracurricular management, sports teams, event organization, and achievement tracking",
    },
    {
      icon: Map,
      title: "GPS Tracking",
      description: "Real-time bus tracking, route optimization, and student pickup/drop-off monitoring",
    },
    {
      icon: Utensils,
      title: "Cafeteria Management",
      description: "Meal planning, dietary requirements, nutrition tracking, and payment integration",
    },
    {
      icon: Database,
      title: "Data Management",
      description: "Secure data storage, automated backups, GDPR compliance, and data export tools",
    },
    {
      icon: Library,
      title: "Digital Library",
      description: "E-books, research databases, digital resources, and reading progress tracking",
    },
    {
      icon: Video,
      title: "E-Learning Platform",
      description: "Online classes, recorded lectures, interactive content, and virtual classrooms",
    },
    {
      icon: Zap,
      title: "Learning Analytics",
      description: "Student engagement tracking, learning outcome analysis, and intervention recommendations",
    },
    {
      icon: Settings,
      title: "Custom Configuration",
      description: "Flexible system settings, custom fields, workflow automation, and business rules",
    },
    {
      icon: Layers,
      title: "Third-party Integration",
      description: "API connectivity, data synchronization, and seamless integration with existing systems",
    },
    {
      icon: Lock,
      title: "Security & Compliance",
      description: "Multi-factor authentication, role-based access, audit trails, and data encryption",
    },
    {
      icon: Wifi,
      title: "API & Webhooks",
      description: "RESTful APIs, real-time webhooks, developer tools, and custom integrations",
    },
    {
      icon: HelpCircle,
      title: "24/7 Support",
      description: "Dedicated support team, comprehensive documentation, video tutorials, and training programs",
    },
    {
      icon: UserCog,
      title: "User Training",
      description: "Onboarding programs, regular training sessions, certification courses, and user adoption support",
    },
  ]

  const displayedFeatures = showAllFeatures 
    ? [...coreFeatures, ...additionalFeatures] 
    : coreFeatures

  const testimonials = [
    {
      name: "Mrs. Sarah Chikwanha",
      role: "Headmaster, Harare Primary School",
      content:
        "OneClass has transformed how we manage our school. Fee collection is now 95% automated and our teachers love the lesson planning tools.",
      rating: 5,
    },
    {
      name: "Mr. David Moyo",
      role: "Administrator, Bulawayo High School",
      content:
        "The parent portal has improved communication dramatically. Parents can now track their children's progress in real-time.",
      rating: 5,
    },
    {
      name: "Ms. Grace Mukamuri",
      role: "Teacher, Mutare Secondary",
      content:
        "Creating assessments and tracking student performance has never been easier. The AI suggestions are incredibly helpful.",
      rating: 5,
    },
  ]

  const pricingPlans = [
    {
      name: "Starter",
      price: "$29",
      period: "per month",
      description: "Perfect for small schools up to 200 students",
      features: ["Up to 200 students", "12 core modules", "Basic SIS & academics", "Parent portal", "Email support", "Mobile app access"],
      popular: false,
    },
    {
      name: "Professional",
      price: "$79",
      period: "per month",
      description: "Ideal for medium schools up to 800 students",
      features: [
        "Up to 800 students",
        "25+ modules included",
        "Full SIS & academics",
        "Finance & billing",
        "Communication hub",
        "AI learning tools",
        "Priority support",
        "Custom branding",
      ],
      popular: true,
    },
    {
      name: "Enterprise",
      price: "$199",
      period: "per month",
      description: "For large schools and school groups",
      features: [
        "Unlimited students",
        "All 36+ modules included",
        "Multi-school management",
        "Advanced analytics & AI",
        "Ministry integration",
        "Transport & GPS tracking",
        "Dedicated support",
        "Custom integrations",
      ],
      popular: false,
    },
  ]

  return (
    <div className="min-h-screen bg-background">
      <style jsx>{`
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        .animate-fadeIn {
          animation: fadeIn 0.5s ease-out;
        }
      `}</style>
      {/* Navigation */}
      <nav className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4">
          <div className="flex h-16 items-center justify-between">
            <div className="flex items-center space-x-2">
              <GraduationCap className="h-8 w-8 text-primary" />
              <span className="text-2xl font-bold">OneClass</span>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              <a href="#features" className="text-sm font-medium hover:text-primary">
                Features
              </a>
              <a href="#pricing" className="text-sm font-medium hover:text-primary">
                Pricing
              </a>
              <a href="#testimonials" className="text-sm font-medium hover:text-primary">
                Testimonials
              </a>
              <Link href="/login" className="text-sm font-medium hover:text-primary">
                Login
              </Link>
              <Link href="/onboarding">
                <Button>Start Free Trial</Button>
              </Link>
            </div>

            {/* Mobile menu button */}
            <button className="md:hidden" onClick={() => setMobileMenuOpen(!mobileMenuOpen)}>
              {mobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>

          {/* Mobile Navigation */}
          {mobileMenuOpen && (
            <div className="md:hidden py-4 space-y-4">
              <a href="#features" className="block text-sm font-medium hover:text-primary">
                Features
              </a>
              <a href="#pricing" className="block text-sm font-medium hover:text-primary">
                Pricing
              </a>
              <a href="#testimonials" className="block text-sm font-medium hover:text-primary">
                Testimonials
              </a>
              <Link href="/login" className="block text-sm font-medium hover:text-primary">
                Login
              </Link>
              <Link href="/onboarding">
                <Button className="w-full">Start Free Trial</Button>
              </Link>
            </div>
          )}
        </div>
      </nav>

      {/* Hero Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <Badge className="mb-4" variant="secondary">
            🇿🇼 Built for Zimbabwean Schools
          </Badge>
          <h1 className="text-4xl md:text-6xl font-bold tracking-tight mb-6">
            The Complete School
            <br />
            <span className="text-primary">Management Platform</span>
          </h1>
          <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
            Transform your school with our all-in-one SaaS platform featuring 36+ integrated modules. From AI-powered learning and comprehensive student management to automated finance, transport tracking, and ministry compliance - everything your school needs in one powerful system.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Link href="/onboarding">
              <Button size="lg" className="text-lg px-8">
                Start Free Trial
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Button size="lg" variant="outline" className="text-lg px-8 bg-transparent">
              <Play className="mr-2 h-5 w-5" />
              Watch Demo
            </Button>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-2xl mx-auto">
            <div>
              <div className="text-3xl font-bold text-primary">500+</div>
              <div className="text-sm text-muted-foreground">Schools</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-primary">50K+</div>
              <div className="text-sm text-muted-foreground">Students</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-primary">2K+</div>
              <div className="text-sm text-muted-foreground">Teachers</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-primary">99.9%</div>
              <div className="text-sm text-muted-foreground">Uptime</div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 px-4 bg-muted/50">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Everything Your School Needs & More</h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-6">
              A complete ecosystem of {showAllFeatures ? "39+ integrated modules" : "core features"} designed specifically for Zimbabwean schools and educational institutions. From student management to AI-powered learning, we've got every aspect of your school covered.
            </p>
            <div className="flex flex-wrap justify-center gap-2 text-sm">
              <Badge variant="secondary">Academic Management</Badge>
              <Badge variant="secondary">Financial Control</Badge>
              <Badge variant="secondary">Communication Hub</Badge>
              <Badge variant="secondary">Health & Safety</Badge>
              <Badge variant="secondary">Transport & Logistics</Badge>
              <Badge variant="secondary">AI-Powered Analytics</Badge>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
            {displayedFeatures.map((feature, index) => (
              <Card 
                key={index} 
                className={`border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 ${
                  index >= coreFeatures.length && showAllFeatures ? 'animate-fadeIn' : ''
                }`}
              >
                <CardHeader className="pb-3">
                  <feature.icon className="h-10 w-10 text-primary mb-3" />
                  <CardTitle className="text-lg leading-tight">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-sm leading-relaxed">{feature.description}</CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center">
            <Button
              variant="outline"
              size="lg"
              onClick={() => setShowAllFeatures(!showAllFeatures)}
              className="text-lg px-8"
            >
              {showAllFeatures ? 'Show Less Features' : `Show All ${coreFeatures.length + additionalFeatures.length} Features`}
              <ArrowRight className={`ml-2 h-5 w-5 transition-transform duration-300 ${showAllFeatures ? 'rotate-90' : ''}`} />
            </Button>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="py-20 px-4">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Trusted by Schools Across Zimbabwe</h2>
            <p className="text-xl text-muted-foreground">See what educators are saying about OneClass</p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="border-0 shadow-lg">
                <CardHeader>
                  <div className="flex items-center space-x-1 mb-2">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                  <CardDescription className="text-base italic">"{testimonial.content}"</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="font-semibold">{testimonial.name}</div>
                  <div className="text-sm text-muted-foreground">{testimonial.role}</div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 px-4 bg-muted/50">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Simple, Transparent Pricing</h2>
            <p className="text-xl text-muted-foreground">
              Choose the plan that fits your school's needs. All plans include a 30-day free trial.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {pricingPlans.map((plan, index) => (
              <Card key={index} className={`relative ${plan.popular ? "border-primary shadow-lg scale-105" : ""}`}>
                {plan.popular && (
                  <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2">Most Popular</Badge>
                )}
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">{plan.name}</CardTitle>
                  <div className="mt-4">
                    <span className="text-4xl font-bold">{plan.price}</span>
                    <span className="text-muted-foreground">/{plan.period}</span>
                  </div>
                  <CardDescription>{plan.description}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <ul className="space-y-2">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                        <span className="text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Link href="/onboarding" className="block">
                    <Button className="w-full" variant={plan.popular ? "default" : "outline"}>
                      Start Free Trial
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Ready to Transform Your School?</h2>
          <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
            Join hundreds of schools already using OneClass to streamline their operations and improve student outcomes.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/onboarding">
              <Button size="lg" className="text-lg px-8">
                Start Your Free Trial
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Button size="lg" variant="outline" className="text-lg px-8 bg-transparent">
              Schedule a Demo
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t bg-muted/50 py-12 px-4">
        <div className="container mx-auto">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <GraduationCap className="h-6 w-6 text-primary" />
                <span className="text-xl font-bold">OneClass</span>
              </div>
              <p className="text-muted-foreground">
                The complete school management platform built for Zimbabwean schools.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Product</h3>
              <ul className="space-y-2 text-muted-foreground">
                <li>
                  <a href="#features">Features</a>
                </li>
                <li>
                  <a href="#pricing">Pricing</a>
                </li>
                <li>
                  <a href="#">Integrations</a>
                </li>
                <li>
                  <a href="#">API</a>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Support</h3>
              <ul className="space-y-2 text-muted-foreground">
                <li>
                  <a href="#">Help Center</a>
                </li>
                <li>
                  <a href="#">Documentation</a>
                </li>
                <li>
                  <a href="#">Contact Us</a>
                </li>
                <li>
                  <a href="#">Status</a>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Company</h3>
              <ul className="space-y-2 text-muted-foreground">
                <li>
                  <a href="#">About</a>
                </li>
                <li>
                  <a href="#">Blog</a>
                </li>
                <li>
                  <a href="#">Careers</a>
                </li>
                <li>
                  <a href="#">Privacy</a>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t mt-8 pt-8 text-center text-muted-foreground">
            <p>&copy; 2024 OneClass. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
