// =====================================================
// Context-Aware Dashboard Router
// File: frontend/app/dashboard/page.tsx
// =====================================================

'use client';

import { useState, useEffect } from 'react';
import { useAuth, useSchoolContext, SchoolRole } from '@/hooks/useAuth';
import { api } from '@/lib/api';
import RoleBasedDashboard from '@/components/dashboard/RoleBasedDashboard';
import { ProgressTracker } from '@/components/progress/ProgressTracker';
import { DashboardLoading, PageLoading } from '@/components/ui/loading';
import { ErrorMessage, NetworkError } from '@/components/ui/error-boundary';
import { AlertTriangle, RefreshCw } from 'lucide-react';

export default function Dashboard() {
  const { user, isLoading: authLoading, isPlatformAdmin } = useAuth();
  const { currentSchool } = useSchoolContext();
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Map our platform roles to the dashboard component's expected format
  const mapUserRole = (): 'admin' | 'teacher' | 'parent' | 'student' => {
    // Super admin sees admin dashboard
    if (isPlatformAdmin) {
      return 'admin';
    }
    
    // School context roles
    if (currentSchool) {
      switch (currentSchool.role) {
        case SchoolRole.PRINCIPAL:
        case SchoolRole.DEPUTY_PRINCIPAL:
        case SchoolRole.ACADEMIC_HEAD:
        case SchoolRole.BURSAR:
        case SchoolRole.REGISTRAR:
          return 'admin';
        case SchoolRole.TEACHER:
        case SchoolRole.FORM_TEACHER:
        case SchoolRole.DEPARTMENT_HEAD:
          return 'teacher';
        case SchoolRole.PARENT:
          return 'parent';
        case SchoolRole.STUDENT:
          return 'student';
        default:
          return 'admin';
      }
    }
    
    // Platform level roles
    switch (user?.platform_role) {
      case 'school_admin':
      case 'registrar':
        return 'admin';
      case 'teacher':
        return 'teacher';
      case 'parent':
        return 'parent';
      case 'student':
        return 'student';
      default:
        return 'admin';
    }
  };

  const userRole = mapUserRole();

  const fetchDashboardData = async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      setError(null);
      
      // Fetch role-specific analytics data
      const analyticsEndpoint = currentSchool 
        ? `/api/v1/schools/${currentSchool.school_id}/analytics/dashboard/${userRole}`
        : `/api/v1/analytics/dashboard/${userRole}`;
        
      const response = await api.get(analyticsEndpoint);
      
      // Enhance data with school context
      const enhancedData = {
        ...response.data,
        school_context: currentSchool ? {
          school_id: currentSchool.school_id,
          school_name: currentSchool.school_name,
          user_role: currentSchool.role,
          permissions: currentSchool.permissions
        } : null,
        user_info: {
          name: `${user.first_name} ${user.last_name}`,
          email: user.email,
          platform_role: user.platform_role,
          is_platform_admin: isPlatformAdmin
        }
      };
      
      setDashboardData(enhancedData);
      
    } catch (error: any) {
      console.error('Error fetching dashboard data:', error);
      
      if (error.response?.status === 401) {
        setError('authentication');
      } else if (error.response?.status === 403) {
        setError('permission');
      } else if (error.code === 'NETWORK_ERROR' || !error.response) {
        setError('network');
      } else {
        setError('generic');
      }
      
      // Set fallback data for graceful degradation
      setDashboardData({
        user_info: {
          name: `${user.first_name} ${user.last_name}`,
          email: user.email,
          platform_role: user.platform_role,
          is_platform_admin: isPlatformAdmin
        },
        school_context: currentSchool
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!authLoading && user) {
      fetchDashboardData();
    }
  }, [user, currentSchool, userRole, authLoading, isPlatformAdmin]);

  // Loading state
  if (authLoading) {
    return <PageLoading type="auth" message="Authenticating..." />;
  }

  if (loading) {
    return <DashboardLoading userRole={userRole} />;
  }

  // Not authenticated
  if (!user) {
    return (
      <ErrorMessage
        type="auth"
        onRetry={() => window.location.href = '/login'}
        className="min-h-screen flex items-center justify-center p-4"
      />
    );
  }

  // No school context for non-platform admin users
  if (!isPlatformAdmin && !currentSchool && userRole !== 'student') {
    return (
      <ErrorMessage
        title="No School Context"
        message="Please select a school to access your dashboard. If you should have access to a school, please contact your administrator."
        type="validation"
        onRetry={() => window.location.reload()}
        className="min-h-screen flex items-center justify-center p-4"
      />
    );
  }

  // Handle specific error states
  if (error) {
    const errorProps = {
      authentication: {
        type: 'auth' as const,
        onRetry: () => window.location.href = '/login'
      },
      permission: {
        type: 'permission' as const,
        onRetry: () => window.history.back()
      },
      network: {
        type: 'network' as const,
        onRetry: fetchDashboardData
      },
      generic: {
        type: 'generic' as const,
        onRetry: fetchDashboardData
      }
    };

    const props = errorProps[error] || errorProps.generic;
    
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <ErrorMessage {...props} />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto">
        {/* Enhanced Role-based Dashboard */}
        <div className="p-6">
          <RoleBasedDashboard 
            userRole={userRole} 
            schoolContext={dashboardData?.school_context}
            analytics={dashboardData}
          />
        </div>
        
        {/* Real-time Progress Tracker */}
        {(userRole === 'admin' || isPlatformAdmin) && (
          <div className="px-6 pb-6">
            <ProgressTracker 
              showAllOperations={true}
              autoRefresh={true}
            />
          </div>
        )}
      </div>
    </div>
  );
}