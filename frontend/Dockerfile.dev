FROM node:22-alpine

# Install required packages for building native dependencies
RUN apk add --no-cache python3 make g++

# Set working directory
WORKDIR /app

# Copy package files first for better layer caching
COPY package*.json ./

# Install dependencies with legacy peer deps to resolve React version conflicts
RUN npm install --legacy-peer-deps

# Copy source code (excluding node_modules via .dockerignore)
COPY . .

# Expose port
EXPOSE 3000

# Start development server with hot reloading
CMD ["npm", "run", "dev", "--", "--hostname", "0.0.0.0", "--port", "3000"]