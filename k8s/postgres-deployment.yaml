apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: oneclass-platform
  labels:
    app.kubernetes.io/name: oneclass-platform
    app.kubernetes.io/component: postgres
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
        app.kubernetes.io/name: oneclass-platform
        app.kubernetes.io/component: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15-alpine
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: oneclass-config
              key: POSTGRES_DB
        - name: POSTGRES_USER
          valueFrom:
            configMapKeyRef:
              name: oneclass-config
              key: POSTGRES_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: oneclass-secrets
              key: DB_PASSWORD
        - name: POSTGRES_MULTIPLE_EXTENSIONS
          value: "pg_trgm,unaccent,uuid-ossp"
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        - name: postgres-init
          mountPath: /docker-entrypoint-initdb.d
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        readinessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - pg_isready -U oneclass -d oneclass_production
          initialDelaySeconds: 10
          periodSeconds: 5
        livenessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - pg_isready -U oneclass -d oneclass_production
          initialDelaySeconds: 30
          periodSeconds: 10
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-pvc
      - name: postgres-init
        configMap:
          name: postgres-init-config

---
apiVersion: v1
kind: Service
metadata:
  name: postgres
  namespace: oneclass-platform
  labels:
    app.kubernetes.io/name: oneclass-platform
    app.kubernetes.io/component: postgres
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
  type: ClusterIP

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
  namespace: oneclass-platform
  labels:
    app.kubernetes.io/name: oneclass-platform
    app.kubernetes.io/component: postgres-storage
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
  storageClassName: standard