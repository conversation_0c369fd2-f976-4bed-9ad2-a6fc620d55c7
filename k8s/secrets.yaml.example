# OneClass Platform - Kubernetes Secrets Template
# Copy this file to secrets.yaml and fill in your actual secrets
# DO NOT commit secrets.yaml to version control!

apiVersion: v1
kind: Secret
metadata:
  name: oneclass-secrets
  namespace: oneclass-platform
  labels:
    app.kubernetes.io/name: oneclass-platform
    app.kubernetes.io/component: secrets
type: Opaque
data:
  # Database credentials (base64 encoded)
  DB_PASSWORD: <base64-encoded-db-password>
  
  # Redis credentials (base64 encoded)
  REDIS_PASSWORD: <base64-encoded-redis-password>
  
  # JWT secret (base64 encoded)
  JWT_SECRET: <base64-encoded-jwt-secret>
  
  # Clerk authentication (base64 encoded)
  CLERK_SECRET_KEY: <base64-encoded-clerk-secret-key>
  NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: <base64-encoded-clerk-publishable-key>
  
  # External services (base64 encoded)
  OPENAI_API_KEY: <base64-encoded-openai-api-key>
  PAYNOW_INTEGRATION_ID: <base64-encoded-paynow-integration-id>
  PAYNOW_INTEGRATION_KEY: <base64-encoded-paynow-integration-key>
  
  # AWS credentials (base64 encoded)
  AWS_ACCESS_KEY_ID: <base64-encoded-aws-access-key-id>
  AWS_SECRET_ACCESS_KEY: <base64-encoded-aws-secret-access-key>
  
  # Email credentials (base64 encoded)
  SMTP_USER: <base64-encoded-smtp-user>
  SMTP_PASSWORD: <base64-encoded-smtp-password>
  
  # Monitoring (base64 encoded)
  SENTRY_DSN: <base64-encoded-sentry-dsn>
  GRAFANA_PASSWORD: <base64-encoded-grafana-password>

---
# Example of how to create secrets:
# echo -n "your-secret-value" | base64
#
# To create the secret file:
# kubectl create secret generic oneclass-secrets \
#   --from-literal=DB_PASSWORD=your-db-password \
#   --from-literal=REDIS_PASSWORD=your-redis-password \
#   --from-literal=JWT_SECRET=your-jwt-secret \
#   --from-literal=CLERK_SECRET_KEY=your-clerk-secret-key \
#   --from-literal=NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=your-clerk-publishable-key \
#   --from-literal=OPENAI_API_KEY=your-openai-api-key \
#   --from-literal=AWS_ACCESS_KEY_ID=your-aws-access-key-id \
#   --from-literal=AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key \
#   --from-literal=SENTRY_DSN=your-sentry-dsn \
#   --namespace=oneclass-platform