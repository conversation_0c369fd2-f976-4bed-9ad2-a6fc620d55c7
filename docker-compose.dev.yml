version: '3.8'

services:
  # Redis for caching
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped

  # Mail server for development
  mailhog:
    image: mailhog/mailhog
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    restart: unless-stopped

  # Backend API service
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    depends_on:
      - redis
      - mailhog
    environment:
      - DATABASE_URL=postgresql://postgres:<EMAIL>:5432/oneclass
      - REDIS_URL=redis://redis:6379
      - SMTP_HOST=mailhog
      - SMTP_PORT=1025
    volumes:
      - ./backend:/app
      - /app/node_modules
    restart: unless-stopped
    develop:
      watch:
        - action: sync
          path: ./backend
          target: /app

volumes:
  redis_data: