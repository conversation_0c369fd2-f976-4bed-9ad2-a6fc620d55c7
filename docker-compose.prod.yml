version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: oneclass-postgres
    environment:
      POSTGRES_DB: oneclass_production
      POSTGRES_USER: oneclass
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_MULTIPLE_EXTENSIONS: pg_trgm,unaccent,uuid-ossp
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - oneclass-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U oneclass -d oneclass_production"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: oneclass-redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - oneclass-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
      args:
        BUILD_ENV: production
        BUILD_VERSION: ${VERSION:-1.0.0}
    container_name: oneclass-backend
    environment:
      # Database
      DATABASE_URL: postgresql://oneclass:${DB_PASSWORD}@postgres:5432/oneclass_production
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379
      
      # Authentication
      JWT_SECRET: ${JWT_SECRET}
      CLERK_SECRET_KEY: ${CLERK_SECRET_KEY}
      
      # External Services
      PAYNOW_INTEGRATION_ID: ${PAYNOW_INTEGRATION_ID}
      PAYNOW_INTEGRATION_KEY: ${PAYNOW_INTEGRATION_KEY}
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      
      # Email
      SMTP_HOST: ${SMTP_HOST}
      SMTP_PORT: ${SMTP_PORT}
      SMTP_USER: ${SMTP_USER}
      SMTP_PASSWORD: ${SMTP_PASSWORD}
      
      # File Storage
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      AWS_S3_BUCKET: ${AWS_S3_BUCKET}
      AWS_REGION: ${AWS_REGION}
      
      # Monitoring
      SENTRY_DSN: ${SENTRY_DSN}
      LOG_LEVEL: info
    volumes:
      - backend_logs:/app/logs
      - backend_uploads:/app/uploads
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    ports:
      - "8000:8000"
    networks:
      - oneclass-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: production
      args:
        NODE_ENV: production
        NEXT_PUBLIC_API_URL: https://api.oneclass.ac.zw
        NEXT_PUBLIC_APP_URL: https://app.oneclass.ac.zw
        NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: ${NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY}
        NEXT_PUBLIC_DEFAULT_DOMAIN: oneclass.ac.zw
    container_name: oneclass-frontend
    environment:
      NODE_ENV: production
      NEXT_PUBLIC_API_URL: https://api.oneclass.ac.zw
      NEXT_PUBLIC_APP_URL: https://app.oneclass.ac.zw
      NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: ${NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY}
      NEXT_PUBLIC_DEFAULT_DOMAIN: oneclass.ac.zw
    depends_on:
      backend:
        condition: service_healthy
    ports:
      - "3000:3000"
    networks:
      - oneclass-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: oneclass-nginx
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/sites-enabled:/etc/nginx/sites-enabled
      - ./ssl:/etc/ssl/certs
      - nginx_logs:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - frontend
      - backend
    networks:
      - oneclass-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Database Backup Service
  backup:
    image: postgres:15-alpine
    container_name: oneclass-backup
    environment:
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      BACKUP_SCHEDULE: ${BACKUP_SCHEDULE:-0 2 * * *}
      BACKUP_RETENTION_DAYS: ${BACKUP_RETENTION_DAYS:-30}
    volumes:
      - postgres_data:/var/lib/postgresql/data:ro
      - backup_data:/backups
      - ./scripts/backup.sh:/backup.sh
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - oneclass-network
    restart: unless-stopped
    command: >
      sh -c "
        apk add --no-cache dcron &&
        echo '${BACKUP_SCHEDULE:-0 2 * * *} /backup.sh' > /etc/crontabs/root &&
        crond -f
      "

  # Monitoring - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: oneclass-prometheus
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - oneclass-network
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  # Monitoring - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: oneclass-grafana
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD}
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    ports:
      - "3001:3000"
    networks:
      - oneclass-network
    restart: unless-stopped
    depends_on:
      - prometheus

networks:
  oneclass-network:
    driver: bridge

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  backend_logs:
    driver: local
  backend_uploads:
    driver: local
  nginx_logs:
    driver: local
  backup_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local