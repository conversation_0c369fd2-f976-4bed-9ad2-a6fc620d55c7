# OneClass Platform - Production Environment Configuration
# Copy this file to .env.production and fill in your actual production values
# NEVER commit production secrets to version control!

# ================================================
# CORE APPLICATION SETTINGS
# ================================================

# Environment
NODE_ENV=production
ENVIRONMENT=production
DEBUG=false

# Application URLs
NEXT_PUBLIC_API_URL=https://api.oneclass.ac.zw
NEXT_PUBLIC_APP_URL=https://app.oneclass.ac.zw
API_BASE_URL=https://api.oneclass.ac.zw

# Domain Configuration
NEXT_PUBLIC_DEFAULT_DOMAIN=oneclass.ac.zw
PRODUCTION_DOMAIN=oneclass.ac.zw
ENABLE_MULTITENANCY=true
CUSTOM_DOMAINS_ENABLED=true

# ================================================
# AUTHENTICATION & SECURITY
# ================================================

# Clerk Authentication (Production Keys)
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_your-production-publishable-key
CLERK_SECRET_KEY=sk_live_your-production-secret-key
CLERK_WEBHOOK_SECRET=whsec_your-webhook-secret

# JWT Configuration
JWT_SECRET=your-super-secure-production-jwt-secret-256-bits-minimum
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
JWT_ALGORITHM=HS256

# NextAuth Configuration
NEXTAUTH_SECRET=your-super-secure-nextauth-production-secret
NEXTAUTH_URL=https://app.oneclass.ac.zw

# Session Configuration
SESSION_SECRET=your-session-secret-key-production
SESSION_TIMEOUT=86400
SESSION_SECURE=true
SESSION_SAME_SITE=strict

# CORS Configuration
CORS_ORIGINS=https://app.oneclass.ac.zw,https://admin.oneclass.ac.zw,https://*.oneclass.ac.zw
CORS_CREDENTIALS=true

# Security Headers
HSTS_MAX_AGE=31536000
CSP_REPORT_URI=https://api.oneclass.ac.zw/security/csp-report

# ================================================
# DATABASE CONFIGURATION
# ================================================

# PostgreSQL Production Database
DATABASE_URL=postgresql://prod_user:<EMAIL>:5432/oneclass_production
DB_HOST=prod-db.oneclass.ac.zw
DB_PORT=5432
DB_NAME=oneclass_production
DB_USER=prod_user
DB_PASSWORD=secure_production_password
DB_SSL=require
DB_POOL_SIZE=20
DB_MAX_CONNECTIONS=100
DB_CONNECTION_TIMEOUT=30000

# Database Migrations
RUN_MIGRATIONS=true
MIGRATIONS_TABLE=schema_migrations

# Read Replicas (optional)
READ_REPLICA_URL=postgresql://readonly_user:<EMAIL>:5432/oneclass_production

# ================================================
# REDIS CONFIGURATION
# ================================================

# Redis for Caching and Sessions
REDIS_URL=redis://prod-redis.oneclass.ac.zw:6379
REDIS_HOST=prod-redis.oneclass.ac.zw
REDIS_PORT=6379
REDIS_PASSWORD=secure_redis_password
REDIS_DB=0
REDIS_TLS=true

# Redis Cluster (if using cluster mode)
REDIS_CLUSTER_NODES=redis-1.oneclass.ac.zw:6379,redis-2.oneclass.ac.zw:6379,redis-3.oneclass.ac.zw:6379

# Cache Configuration
CACHE_TTL=3600
CACHE_MAX_SIZE=1000
SESSION_STORE=redis

# ================================================
# FILE STORAGE
# ================================================

# AWS S3 Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=AKIA_your_production_access_key
AWS_SECRET_ACCESS_KEY=your_production_secret_access_key
AWS_S3_BUCKET=oneclass-production-files
AWS_S3_REGION=us-east-1
AWS_CLOUDFRONT_DOMAIN=cdn.oneclass.ac.zw

# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=pdf,doc,docx,xls,xlsx,jpg,jpeg,png,gif
FILE_ENCRYPTION=true

# ================================================
# EMAIL CONFIGURATION
# ================================================

# SMTP Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=true
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_smtp_app_password
SMTP_FROM_NAME=OneClass Platform
SMTP_FROM_EMAIL=<EMAIL>

# Email Templates
EMAIL_TEMPLATES_ENABLED=true
EMAIL_TEMPLATE_PATH=/app/templates/email

# Bulk Email Service (optional)
SENDGRID_API_KEY=SG.your_sendgrid_api_key
MAILGUN_API_KEY=your_mailgun_api_key
MAILGUN_DOMAIN=mg.oneclass.ac.zw

# ================================================
# EXTERNAL SERVICES
# ================================================

# Paynow Payment Gateway (Zimbabwe)
PAYNOW_INTEGRATION_ID=your_production_paynow_id
PAYNOW_INTEGRATION_KEY=your_production_paynow_key
PAYNOW_RETURN_URL=https://app.oneclass.ac.zw/payments/return
PAYNOW_RESULT_URL=https://api.oneclass.ac.zw/webhooks/paynow

# OpenAI API (for AI features)
OPENAI_API_KEY=sk-your-production-openai-api-key
OPENAI_ORG_ID=org-your-openai-organization-id
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=1000

# SMS Service (for notifications)
TWILIO_ACCOUNT_SID=ACyour_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********

# Geocoding Service
GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# ================================================
# MONITORING & LOGGING
# ================================================

# Application Performance Monitoring
SENTRY_DSN=https://<EMAIL>/project-id
SENTRY_ENVIRONMENT=production
SENTRY_TRACES_SAMPLE_RATE=0.1
SENTRY_PROFILES_SAMPLE_RATE=0.1

# New Relic (optional)
NEW_RELIC_LICENSE_KEY=your_new_relic_license_key
NEW_RELIC_APP_NAME=OneClass-Production

# DataDog (optional)
DATADOG_API_KEY=your_datadog_api_key
DATADOG_APP_KEY=your_datadog_app_key

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json
LOG_DESTINATION=file
LOG_FILE_PATH=/var/log/oneclass/app.log
LOG_MAX_SIZE=100MB
LOG_MAX_FILES=10

# ================================================
# RATE LIMITING
# ================================================

# Rate Limiting Configuration
RATE_LIMIT_ENABLED=true
RATE_LIMIT_WINDOW=15m
RATE_LIMIT_MAX_REQUESTS=1000
RATE_LIMIT_SKIP_TRUSTED=true

# API Rate Limits
API_RATE_LIMIT_PER_MINUTE=60
AUTH_RATE_LIMIT_PER_MINUTE=5
UPLOAD_RATE_LIMIT_PER_MINUTE=10

# ================================================
# BACKUP & DISASTER RECOVERY
# ================================================

# Database Backup
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=oneclass-production-backups
BACKUP_ENCRYPTION_KEY=your_backup_encryption_key

# File Backup
FILE_BACKUP_ENABLED=true
FILE_BACKUP_SCHEDULE=0 3 * * *

# ================================================
# HEALTH CHECKS & UPTIME
# ================================================

# Health Check Configuration
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=5
HEALTH_CHECK_ENDPOINT=/health

# Uptime Monitoring
UPTIME_ROBOT_API_KEY=your_uptime_robot_api_key
PINGDOM_API_KEY=your_pingdom_api_key

# ================================================
# ANALYTICS & TRACKING
# ================================================

# Google Analytics
NEXT_PUBLIC_GA_TRACKING_ID=GA-XXXXXXXXX-X
GOOGLE_ANALYTICS_PROPERTY_ID=your_ga_property_id

# Facebook Pixel (optional)
NEXT_PUBLIC_FACEBOOK_PIXEL_ID=your_facebook_pixel_id

# Custom Analytics
ANALYTICS_ENABLED=true
ANALYTICS_ENDPOINT=https://analytics.oneclass.ac.zw

# ================================================
# FEATURE FLAGS
# ================================================

# Feature Flags
FEATURE_AI_ASSISTANCE=true
FEATURE_ADVANCED_REPORTING=true
FEATURE_MOBILE_APP=true
FEATURE_BULK_OPERATIONS=true
FEATURE_CUSTOM_DOMAINS=true
FEATURE_SSO=true
FEATURE_API_ACCESS=true

# Experimental Features
FEATURE_BETA_DASHBOARD=false
FEATURE_ML_INSIGHTS=false
FEATURE_BLOCKCHAIN_CERTIFICATES=false

# ================================================
# COMPLIANCE & AUDIT
# ================================================

# Data Protection
GDPR_COMPLIANCE=true
DATA_RETENTION_DAYS=2555
AUDIT_LOGGING=true
AUDIT_LOG_RETENTION_DAYS=2555

# Zimbabwe Compliance
ZIMBOL_COMPLIANCE=true
MINISTRY_REPORTING=true
LOCAL_DATA_STORAGE=true

# ================================================
# PERFORMANCE OPTIMIZATION
# ================================================

# Caching
ENABLE_REDIS_CACHE=true
ENABLE_CDN=true
CDN_URL=https://cdn.oneclass.ac.zw

# Compression
ENABLE_GZIP=true
ENABLE_BROTLI=true

# Image Optimization
IMAGE_OPTIMIZATION=true
IMAGE_QUALITY=85
IMAGE_FORMATS=webp,avif,jpeg

# Database Optimization
DB_QUERY_CACHE=true
DB_SLOW_QUERY_LOG=true
DB_SLOW_QUERY_THRESHOLD=1000

# ================================================
# SSL/TLS CONFIGURATION
# ================================================

# SSL Certificate
SSL_CERTIFICATE_PATH=/etc/ssl/certs/oneclass.crt
SSL_PRIVATE_KEY_PATH=/etc/ssl/private/oneclass.key
SSL_CA_BUNDLE_PATH=/etc/ssl/certs/ca-bundle.crt

# HTTPS Configuration
FORCE_HTTPS=true
HTTPS_PORT=443
REDIRECT_HTTP_TO_HTTPS=true

# ================================================
# KUBERNETES/DOCKER CONFIGURATION
# ================================================

# Container Configuration
CONTAINER_PORT=3000
API_CONTAINER_PORT=8000
HEALTH_CHECK_PORT=3001

# Resource Limits
MEMORY_LIMIT=2Gi
CPU_LIMIT=1000m
MEMORY_REQUEST=1Gi
CPU_REQUEST=500m

# Scaling
MIN_REPLICAS=2
MAX_REPLICAS=10
TARGET_CPU_UTILIZATION=70

# ================================================
# TIMEZONE & LOCALIZATION
# ================================================

# Timezone
TZ=Africa/Harare
DEFAULT_TIMEZONE=Africa/Harare

# Localization
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=en,sn,nd
CURRENCY_CODE=USD
COUNTRY_CODE=ZW

# ================================================
# MAINTENANCE MODE
# ================================================

# Maintenance
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE=OneClass Platform is currently under maintenance. Please try again later.
MAINTENANCE_ALLOWED_IPS=127.0.0.1,::1

# ================================================
# WEBHOOK CONFIGURATION
# ================================================

# Webhook Settings
WEBHOOK_SECRET=your_webhook_secret_key
WEBHOOK_TIMEOUT=30000
WEBHOOK_RETRY_ATTEMPTS=3

# Third-party Webhooks
CLERK_WEBHOOK_SECRET=whsec_your_clerk_webhook_secret
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret
PAYNOW_WEBHOOK_SECRET=your_paynow_webhook_secret