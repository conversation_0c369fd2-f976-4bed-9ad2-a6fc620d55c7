# OneClass Platform - Development Environment Configuration
# Copy this file to .env.development or .env.local and fill in your actual development values
# Use local services and test credentials for development

# ================================================
# CORE APPLICATION SETTINGS
# ================================================

# Environment
NODE_ENV=development
ENVIRONMENT=development
DEBUG=true

# Application URLs
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_APP_URL=http://localhost:3000
API_BASE_URL=http://localhost:8000

# Domain Configuration
NEXT_PUBLIC_DEFAULT_DOMAIN=localhost:3000
PRODUCTION_DOMAIN=localhost:3000
ENABLE_MULTITENANCY=true
CUSTOM_DOMAINS_ENABLED=false

# ================================================
# AUTHENTICATION & SECURITY
# ================================================

# Clerk Authentication (Development Keys)
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_your-development-publishable-key
CLERK_SECRET_KEY=sk_test_your-development-secret-key
CLERK_WEBHOOK_SECRET=whsec_your-development-webhook-secret

# JWT Configuration
JWT_SECRET=development-jwt-secret-change-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
JWT_ALGORITHM=HS256

# NextAuth Configuration
NEXTAUTH_SECRET=development-nextauth-secret-change-in-production
NEXTAUTH_URL=http://localhost:3000

# Session Configuration
SESSION_SECRET=development-session-secret-key
SESSION_TIMEOUT=86400
SESSION_SECURE=false
SESSION_SAME_SITE=lax

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://127.0.0.1:3000
CORS_CREDENTIALS=true

# ================================================
# DATABASE CONFIGURATION
# ================================================

# PostgreSQL Development Database
DATABASE_URL=postgresql://oneclass:dev_password@localhost:5432/oneclass_dev
DB_HOST=localhost
DB_PORT=5432
DB_NAME=oneclass_dev
DB_USER=oneclass
DB_PASSWORD=dev_password
DB_SSL=disable
DB_POOL_SIZE=5
DB_MAX_CONNECTIONS=20
DB_CONNECTION_TIMEOUT=30000

# Database Migrations
RUN_MIGRATIONS=true
MIGRATIONS_TABLE=schema_migrations

# ================================================
# REDIS CONFIGURATION
# ================================================

# Redis for Caching and Sessions (local)
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_TLS=false

# Cache Configuration
CACHE_TTL=600
CACHE_MAX_SIZE=100
SESSION_STORE=redis

# ================================================
# FILE STORAGE
# ================================================

# Local File Storage (development)
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=minioadmin
AWS_SECRET_ACCESS_KEY=minioadmin
AWS_S3_BUCKET=oneclass-dev-files
AWS_S3_REGION=us-east-1
AWS_ENDPOINT_URL=http://localhost:9000
AWS_CLOUDFRONT_DOMAIN=localhost:9000

# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=pdf,doc,docx,xls,xlsx,jpg,jpeg,png,gif,txt
FILE_ENCRYPTION=false

# ================================================
# EMAIL CONFIGURATION
# ================================================

# SMTP Configuration (MailHog for development)
SMTP_HOST=localhost
SMTP_PORT=1025
SMTP_SECURE=false
SMTP_USER=
SMTP_PASSWORD=
SMTP_FROM_NAME=OneClass Platform (Dev)
SMTP_FROM_EMAIL=<EMAIL>

# Email Templates
EMAIL_TEMPLATES_ENABLED=true
EMAIL_TEMPLATE_PATH=/app/templates/email

# ================================================
# EXTERNAL SERVICES
# ================================================

# Paynow Payment Gateway (Test Environment)
PAYNOW_INTEGRATION_ID=test_paynow_id
PAYNOW_INTEGRATION_KEY=test_paynow_key
PAYNOW_RETURN_URL=http://localhost:3000/payments/return
PAYNOW_RESULT_URL=http://localhost:8000/webhooks/paynow

# OpenAI API (for AI features - use test key)
OPENAI_API_KEY=sk-your-development-openai-api-key
OPENAI_ORG_ID=org-your-openai-organization-id
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=500

# SMS Service (test/mock service)
TWILIO_ACCOUNT_SID=test_twilio_account_sid
TWILIO_AUTH_TOKEN=test_twilio_auth_token
TWILIO_PHONE_NUMBER=+***********

# Geocoding Service
GOOGLE_MAPS_API_KEY=your_development_google_maps_api_key

# ================================================
# MONITORING & LOGGING
# ================================================

# Application Performance Monitoring (disabled for dev)
SENTRY_DSN=
SENTRY_ENVIRONMENT=development
SENTRY_TRACES_SAMPLE_RATE=0
SENTRY_PROFILES_SAMPLE_RATE=0

# Logging Configuration
LOG_LEVEL=debug
LOG_FORMAT=pretty
LOG_DESTINATION=console
LOG_FILE_PATH=./logs/dev.log

# ================================================
# RATE LIMITING
# ================================================

# Rate Limiting Configuration (very lenient for development)
RATE_LIMIT_ENABLED=false
RATE_LIMIT_WINDOW=15m
RATE_LIMIT_MAX_REQUESTS=10000
RATE_LIMIT_SKIP_TRUSTED=true

# API Rate Limits
API_RATE_LIMIT_PER_MINUTE=1000
AUTH_RATE_LIMIT_PER_MINUTE=100
UPLOAD_RATE_LIMIT_PER_MINUTE=100

# ================================================
# BACKUP & DISASTER RECOVERY
# ================================================

# Database Backup (disabled for development)
BACKUP_ENABLED=false
BACKUP_SCHEDULE=0 6 * * *
BACKUP_RETENTION_DAYS=3
BACKUP_S3_BUCKET=oneclass-dev-backups
BACKUP_ENCRYPTION_KEY=dev_backup_encryption_key

# ================================================
# FEATURE FLAGS
# ================================================

# Feature Flags (enable all for development)
FEATURE_AI_ASSISTANCE=true
FEATURE_ADVANCED_REPORTING=true
FEATURE_MOBILE_APP=true
FEATURE_BULK_OPERATIONS=true
FEATURE_CUSTOM_DOMAINS=false
FEATURE_SSO=true
FEATURE_API_ACCESS=true

# Experimental Features (all enabled for testing)
FEATURE_BETA_DASHBOARD=true
FEATURE_ML_INSIGHTS=true
FEATURE_BLOCKCHAIN_CERTIFICATES=true

# ================================================
# ANALYTICS & TRACKING
# ================================================

# Google Analytics (disabled for development)
NEXT_PUBLIC_GA_TRACKING_ID=
GOOGLE_ANALYTICS_PROPERTY_ID=

# Custom Analytics (disabled for development)
ANALYTICS_ENABLED=false
ANALYTICS_ENDPOINT=http://localhost:8080/analytics

# ================================================
# HEALTH CHECKS & UPTIME
# ================================================

# Health Check Configuration
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=60
HEALTH_CHECK_TIMEOUT=10
HEALTH_CHECK_ENDPOINT=/health

# ================================================
# TIMEZONE & LOCALIZATION
# ================================================

# Timezone
TZ=Africa/Harare
DEFAULT_TIMEZONE=Africa/Harare

# Localization
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=en,sn,nd
CURRENCY_CODE=USD
COUNTRY_CODE=ZW

# ================================================
# MAINTENANCE MODE
# ================================================

# Maintenance
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE=OneClass Platform (Development) is currently under maintenance.
MAINTENANCE_ALLOWED_IPS=127.0.0.1,::1,localhost

# ================================================
# WEBHOOK CONFIGURATION
# ================================================

# Webhook Settings
WEBHOOK_SECRET=development_webhook_secret_key
WEBHOOK_TIMEOUT=30000
WEBHOOK_RETRY_ATTEMPTS=1

# Third-party Webhooks
CLERK_WEBHOOK_SECRET=whsec_development_clerk_webhook_secret
STRIPE_WEBHOOK_SECRET=whsec_development_stripe_webhook_secret
PAYNOW_WEBHOOK_SECRET=development_paynow_webhook_secret

# ================================================
# DEVELOPMENT-SPECIFIC SETTINGS
# ================================================

# Hot Reload & Development Tools
FAST_REFRESH=true
NEXT_TELEMETRY_DISABLED=1
DISABLE_ESLINT=false
TYPESCRIPT_CHECK=true

# Development Database Seeding
SEED_DATABASE=true
SEED_DEMO_DATA=true
CREATE_DEMO_SCHOOLS=true

# Mock Services
USE_MOCK_PAYMENTS=true
USE_MOCK_SMS=true
USE_MOCK_EMAIL=true
MOCK_EXTERNAL_APIS=true

# Performance Monitoring (development)
ENABLE_BUNDLE_ANALYZER=false
ENABLE_SOURCE_MAPS=true
ENABLE_PROFILER=false

# Security (relaxed for development)
DISABLE_CSRF=true
ALLOW_HTTP=true
SKIP_SSL_VERIFICATION=true